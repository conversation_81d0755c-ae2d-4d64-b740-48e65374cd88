package ai.pricefox.mallfox.controller.product;

import ai.pricefox.mallfox.controller.admin.product.CategoryController;
import ai.pricefox.mallfox.service.standard.StandardCategoryService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.*;
import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * CategoryController 单元测试类
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("CategoryController 接口测试")
class CategoryControllerTest {

    @Mock
    private StandardCategoryService standardCategoryService;

    @InjectMocks
    private CategoryController categoryController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(categoryController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @Order(1)
    @DisplayName("测试创建分类 - 成功")
    void testCreateCategory_Success() throws Exception {
        // 准备测试数据
        CategoryInfoCreateReqVO reqVO = new CategoryInfoCreateReqVO();
        reqVO.setParentId(0L);
        reqVO.setName("测试分类");
        reqVO.setLevel(1);
        reqVO.setIconUrl("https://example.com/icon.png");
        reqVO.setSortOrder(1);
        reqVO.setIsActive(true);

        CategoryInfoRespVO respVO = new CategoryInfoRespVO();
        respVO.setId(1L);
        respVO.setParentId(0L);
        respVO.setName("测试分类");
        respVO.setLevel(1);
        respVO.setIconUrl("https://example.com/icon.png");
        respVO.setSortOrder(1);
        respVO.setIsActive(true);
        respVO.setCreateTime(LocalDateTime.now());

        CommonResult<CategoryInfoRespVO> mockResult = CommonResult.success(respVO);

        // Mock 服务层方法
        when(standardCategoryService.createCategoryInfo(any(CategoryInfoCreateReqVO.class)))
                .thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(post("/admin/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("测试分类"))
                .andExpect(jsonPath("$.data.parentId").value(0))
                .andExpect(jsonPath("$.data.level").value(1));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).createCategoryInfo(any(CategoryInfoCreateReqVO.class));
    }

    @Test
    @Order(2)
    @DisplayName("测试更新分类 - 成功")
    void testUpdateCategory_Success() throws Exception {
        // 准备测试数据
        CategoryInfoUpdateReqVO reqVO = new CategoryInfoUpdateReqVO();
        reqVO.setId(1L);
        reqVO.setParentId(0L);
        reqVO.setName("更新后的分类");
        reqVO.setLevel(1);
        reqVO.setIconUrl("https://example.com/new-icon.png");
        reqVO.setSortOrder(2);
        reqVO.setIsActive(true);

        CategoryInfoRespVO respVO = new CategoryInfoRespVO();
        respVO.setId(1L);
        respVO.setParentId(0L);
        respVO.setName("更新后的分类");
        respVO.setLevel(1);
        respVO.setIconUrl("https://example.com/new-icon.png");
        respVO.setSortOrder(2);
        respVO.setIsActive(true);

        CommonResult<CategoryInfoRespVO> mockResult = CommonResult.success(respVO);

        // Mock 服务层方法
        when(standardCategoryService.updateCategoryInfo(any(CategoryInfoUpdateReqVO.class)))
                .thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(put("/admin/v1/categories/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("更新后的分类"))
                .andExpect(jsonPath("$.data.sortOrder").value(2));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).updateCategoryInfo(any(CategoryInfoUpdateReqVO.class));
    }

    @Test
    @Order(3)
    @DisplayName("测试根据ID获取分类 - 成功")
    void testGetCategoryById_Success() throws Exception {
        // 准备测试数据
        CategoryInfoRespVO respVO = new CategoryInfoRespVO();
        respVO.setId(1L);
        respVO.setParentId(0L);
        respVO.setName("测试分类");
        respVO.setLevel(1);
        respVO.setIsActive(true);

        CommonResult<CategoryInfoRespVO> mockResult = CommonResult.success(respVO);

        // Mock 服务层方法
        when(standardCategoryService.getCategoryInfoById(1L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/admin/v1/categories/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.id").value(1))
                .andExpect(jsonPath("$.data.name").value("测试分类"));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).getCategoryInfoById(1L);
    }

    @Test
    @Order(4)
    @DisplayName("测试分页查询分类 - 成功")
    void testGetCategoryPage_Success() throws Exception {
        // 准备测试数据
        CategoryInfoRespVO respVO1 = new CategoryInfoRespVO();
        respVO1.setId(1L);
        respVO1.setName("分类1");
        respVO1.setLevel(1);

        CategoryInfoRespVO respVO2 = new CategoryInfoRespVO();
        respVO2.setId(2L);
        respVO2.setName("分类2");
        respVO2.setLevel(1);

        List<CategoryInfoRespVO> list = Arrays.asList(respVO1, respVO2);
        PageResult<CategoryInfoRespVO> pageResult = new PageResult<>(list, 2L);
        CommonResult<PageResult<CategoryInfoRespVO>> mockResult = CommonResult.success(pageResult);

        // Mock 服务层方法
        when(standardCategoryService.getCategoryInfoPage(any(CategoryInfoPageReqVO.class)))
                .thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/admin/v1/categories")
                        .param("pageNo", "1")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.total").value(2));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).getCategoryInfoPage(any(CategoryInfoPageReqVO.class));
    }

    @Test
    @Order(5)
    @DisplayName("测试获取所有分类列表 - 成功")
    void testGetAllCategories_Success() throws Exception {
        // 准备测试数据
        CategoryInfoRespVO respVO1 = new CategoryInfoRespVO();
        respVO1.setId(1L);
        respVO1.setName("分类1");

        CategoryInfoRespVO respVO2 = new CategoryInfoRespVO();
        respVO2.setId(2L);
        respVO2.setName("分类2");

        List<CategoryInfoRespVO> list = Arrays.asList(respVO1, respVO2);
        CommonResult<List<CategoryInfoRespVO>> mockResult = CommonResult.success(list);

        // Mock 服务层方法
        when(standardCategoryService.getAllCategoryInfos()).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/admin/v1/categories/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).getAllCategoryInfos();
    }

    @Test
    @Order(6)
    @DisplayName("测试获取分类树 - 成功")
    void testGetCategoryTree_Success() throws Exception {
        // 准备测试数据 - 模拟树形结构
        List<Tree<Long>> treeList = Arrays.asList(); // 简化的树形数据
        CommonResult<List<Tree<Long>>> mockResult = CommonResult.success(treeList);

        // Mock 服务层方法
        when(standardCategoryService.getCategoryInfoTree()).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/admin/v1/categories/tree"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray());

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).getCategoryInfoTree();
    }

    @Test
    @Order(7)
    @DisplayName("测试根据父分类ID获取子分类 - 成功")
    void testGetCategoriesByParentId_Success() throws Exception {
        // 准备测试数据
        CategoryInfoRespVO respVO = new CategoryInfoRespVO();
        respVO.setId(2L);
        respVO.setParentId(1L);
        respVO.setName("子分类");
        respVO.setLevel(2);

        List<CategoryInfoRespVO> list = Arrays.asList(respVO);
        CommonResult<List<CategoryInfoRespVO>> mockResult = CommonResult.success(list);

        // Mock 服务层方法
        when(standardCategoryService.getCategoryInfosByParentId(1L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(get("/admin/v1/categories/parent/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].parentId").value(1));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).getCategoryInfosByParentId(1L);
    }

    @Test
    @Order(8)
    @DisplayName("测试删除分类 - 成功")
    void testDeleteCategory_Success() throws Exception {
        // 准备测试数据
        CommonResult<Boolean> mockResult = CommonResult.success(true);

        // Mock 服务层方法
        when(standardCategoryService.deleteCategoryInfo(1L)).thenReturn(mockResult);

        // 执行测试
        mockMvc.perform(delete("/admin/v1/categories/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").value(true));

        // 验证服务层方法被调用
        verify(standardCategoryService, times(1)).deleteCategoryInfo(1L);
    }
}
