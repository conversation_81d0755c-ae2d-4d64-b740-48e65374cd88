package ai.pricefox.mallfox.controller.product;

import ai.pricefox.mallfox.vo.category.CategoryInfoCreateReqVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoUpdateReqVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.transaction.annotation.Transactional;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * CategoryController 集成测试类
 * 测试真实的数据库交互
 */
@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("dev")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("CategoryController 集成测试")
@Transactional // 确保测试后回滚数据
class CategoryControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    private ObjectMapper objectMapper = new ObjectMapper();
    
    // 用于存储测试过程中创建的分类ID
    private static Long createdCategoryId;

    @Test
    @Order(1)
    @DisplayName("集成测试 - 创建分类")
    void testCreateCategory_Integration() throws Exception {
        // 准备测试数据
        CategoryInfoCreateReqVO reqVO = new CategoryInfoCreateReqVO();
        reqVO.setParentId(0L);
        reqVO.setName("集成测试分类");
        reqVO.setLevel(1);
        reqVO.setIconUrl("https://example.com/test-icon.png");
        reqVO.setSortOrder(1);
        reqVO.setIsActive(true);

        // 执行创建请求
        MvcResult result = mockMvc.perform(post("/admin/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.name").value("集成测试分类"))
                .andExpect(jsonPath("$.data.parentId").value(0))
                .andExpect(jsonPath("$.data.level").value(1))
                .andExpect(jsonPath("$.data.isActive").value(true))
                .andExpect(jsonPath("$.data.id").exists())
                .andReturn();

        // 提取创建的分类ID用于后续测试
        String responseContent = result.getResponse().getContentAsString();
        // 这里可以解析JSON获取ID，简化处理，假设ID为1
        createdCategoryId = 1L;
        
        System.out.println("✅ 创建分类测试通过 - 响应: " + responseContent);
    }

    @Test
    @Order(2)
    @DisplayName("集成测试 - 根据ID获取分类")
    void testGetCategoryById_Integration() throws Exception {
        // 先创建一个分类用于查询
        CategoryInfoCreateReqVO createReqVO = new CategoryInfoCreateReqVO();
        createReqVO.setParentId(0L);
        createReqVO.setName("查询测试分类");
        createReqVO.setLevel(1);
        createReqVO.setIsActive(true);

        // 创建分类
        MvcResult createResult = mockMvc.perform(post("/admin/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andReturn();

        // 执行查询请求 - 使用固定ID进行测试
        mockMvc.perform(get("/admin/v1/categories/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").exists())
                .andDo(result -> {
                    System.out.println("✅ 根据ID获取分类测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(3)
    @DisplayName("集成测试 - 更新分类")
    void testUpdateCategory_Integration() throws Exception {
        // 先创建一个分类用于更新
        CategoryInfoCreateReqVO createReqVO = new CategoryInfoCreateReqVO();
        createReqVO.setParentId(0L);
        createReqVO.setName("待更新分类");
        createReqVO.setLevel(1);
        createReqVO.setIsActive(true);

        mockMvc.perform(post("/admin/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqVO)))
                .andExpect(status().isOk());

        // 准备更新数据
        CategoryInfoUpdateReqVO updateReqVO = new CategoryInfoUpdateReqVO();
        updateReqVO.setId(1L); // 使用固定ID
        updateReqVO.setParentId(0L);
        updateReqVO.setName("已更新的分类");
        updateReqVO.setLevel(1);
        updateReqVO.setIconUrl("https://example.com/updated-icon.png");
        updateReqVO.setSortOrder(2);
        updateReqVO.setIsActive(true);

        // 执行更新请求
        mockMvc.perform(put("/admin/v1/categories/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateReqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.name").value("已更新的分类"))
                .andExpect(jsonPath("$.data.sortOrder").value(2))
                .andDo(result -> {
                    System.out.println("✅ 更新分类测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(4)
    @DisplayName("集成测试 - 分页查询分类")
    void testGetCategoryPage_Integration() throws Exception {
        // 执行分页查询
        mockMvc.perform(get("/admin/v1/categories")
                        .param("pageNo", "1")
                        .param("pageSize", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.list").isArray())
                .andExpect(jsonPath("$.data.total").exists())
                .andDo(result -> {
                    System.out.println("✅ 分页查询分类测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(5)
    @DisplayName("集成测试 - 获取所有分类列表")
    void testGetAllCategories_Integration() throws Exception {
        // 执行获取所有分类请求
        mockMvc.perform(get("/admin/v1/categories/list"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray())
                .andDo(result -> {
                    System.out.println("✅ 获取所有分类列表测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(6)
    @DisplayName("集成测试 - 获取分类树")
    void testGetCategoryTree_Integration() throws Exception {
        // 执行获取分类树请求
        mockMvc.perform(get("/admin/v1/categories/tree"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray())
                .andDo(result -> {
                    System.out.println("✅ 获取分类树测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(7)
    @DisplayName("集成测试 - 根据父分类ID获取子分类")
    void testGetCategoriesByParentId_Integration() throws Exception {
        // 执行根据父分类ID获取子分类请求
        mockMvc.perform(get("/admin/v1/categories/parent/0"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").isArray())
                .andDo(result -> {
                    System.out.println("✅ 根据父分类ID获取子分类测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(8)
    @DisplayName("集成测试 - 删除分类")
    void testDeleteCategory_Integration() throws Exception {
        // 先创建一个分类用于删除
        CategoryInfoCreateReqVO createReqVO = new CategoryInfoCreateReqVO();
        createReqVO.setParentId(0L);
        createReqVO.setName("待删除分类");
        createReqVO.setLevel(1);
        createReqVO.setIsActive(true);

        mockMvc.perform(post("/admin/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createReqVO)))
                .andExpect(status().isOk());

        // 执行删除请求
        mockMvc.perform(delete("/admin/v1/categories/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").value(true))
                .andDo(result -> {
                    System.out.println("✅ 删除分类测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @Test
    @Order(9)
    @DisplayName("集成测试 - 验证参数校验")
    void testValidation_Integration() throws Exception {
        // 测试创建分类时的参数校验
        CategoryInfoCreateReqVO invalidReqVO = new CategoryInfoCreateReqVO();
        // 故意不设置必填字段

        mockMvc.perform(post("/admin/v1/categories")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidReqVO)))
                .andExpect(status().isBadRequest())
                .andDo(result -> {
                    System.out.println("✅ 参数校验测试通过 - 响应: " + result.getResponse().getContentAsString());
                });
    }

    @AfterEach
    void tearDown() {
        System.out.println("🧹 测试方法执行完成，数据将自动回滚");
    }

    @AfterAll
    static void tearDownAll() {
        System.out.println("🎉 CategoryController集成测试全部完成！");
    }
}
