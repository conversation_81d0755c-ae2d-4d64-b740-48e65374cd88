package ai.pricefox.mallfox.service;

import ai.pricefox.mallfox.model.param.ProductSearchRequest;
import ai.pricefox.mallfox.model.response.ProductSearchResponse;
import ai.pricefox.mallfox.service.product.ProductSearchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 简化的商品搜索功能测试
 * @since 2025/7/8
 */
@SpringBootTest
public class ProductSearchServiceSimpleTest {

    @Autowired
    private ProductSearchService productSearchService;

    @Test
    public void testBasicSearch() {
        // 基础搜索测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSortBy("relevance");

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("=== 基础搜索测试 ===");
        System.out.println("关键词: " + request.getKeyword());
        System.out.println("总结果数: " + response.getTotal());
        System.out.println("当前页商品数: " + (response.getProducts() != null ? response.getProducts().size() : 0));
        
        if (response.getSearchStats() != null) {
            System.out.println("搜索耗时: " + response.getSearchStats().getSearchTime() + "ms");
        }
    }

    @Test
    public void testPriceRangeSearch() {
        // 价格区间搜索测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        request.setMinPrice(500.0);
        request.setMaxPrice(1200.0);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSortBy("price_asc");

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 价格区间搜索测试 ===");
        System.out.println("价格区间: $" + request.getMinPrice() + " - $" + request.getMaxPrice());
        System.out.println("总结果数: " + response.getTotal());
    }

    @Test
    public void testSpecFiltersSearch() {
        // 规格筛选测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        
        // 设置区间筛选
        request.setMinScreenSize(6.0);
        request.setMaxScreenSize(6.8);
        request.setMinBatteryCapacity(3000);
        request.setMaxBatteryCapacity(4000);
        
        // 设置固定值筛选
        request.setRamSizes(List.of(6, 8));
        request.setColors(List.of("Black", "White"));
        //request.setStorageCapacities(List.of("128GB", "256GB"));
        
        request.setPageNo(1);
        request.setPageSize(10);

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 规格筛选搜索测试 ===");
        System.out.println("屏幕尺寸: " + request.getMinScreenSize() + "-" + request.getMaxScreenSize() + " inches");
        System.out.println("电池容量: " + request.getMinBatteryCapacity() + "-" + request.getMaxBatteryCapacity() + " mAh");
        System.out.println("内存: " + request.getRamSizes() + " GB");
        System.out.println("颜色: " + request.getColors());
        System.out.println("存储: " + request.getStorageCapacities());
        System.out.println("总结果数: " + response.getTotal());
    }

    @Test
    public void testAvailableFilters() {
        // 可用筛选器测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 可用筛选器测试 ===");
        
        if (response.getAvailableFilters() != null) {
            // 品牌筛选器
            if (response.getAvailableFilters().getBrands() != null) {
                System.out.println("可用品牌:");
                response.getAvailableFilters().getBrands().stream().limit(5).forEach(brand -> {
                    System.out.println("  " + brand.getLabel() + " (" + brand.getCount() + ")");
                });
            }

            // 规格筛选器
            if (response.getAvailableFilters().getSpecFilters() != null) {
                System.out.println("\n可用规格筛选器:");
                response.getAvailableFilters().getSpecFilters().forEach(specFilter -> {
                    System.out.println("  " + specFilter.getAttributeName() + ":");
                    if (specFilter.getOptions() != null) {
                        specFilter.getOptions().stream().limit(3).forEach(option -> {
                            System.out.println("    " + option.getLabel() + " (" + option.getCount() + ")");
                        });
                    }
                });
            }

            // 价格统计
            if (response.getSearchStats() != null && response.getSearchStats().getPriceRange() != null) {
                System.out.println("\n价格统计:");
                System.out.println("  最低价: $" + response.getSearchStats().getPriceRange().getMinPrice());
                System.out.println("  最高价: $" + response.getSearchStats().getPriceRange().getMaxPrice());
                System.out.println("  平均价: $" + response.getSearchStats().getPriceRange().getAvgPrice());
            }
        }
    }

    @Test
    public void testSortingOptions() {
        // 排序选项测试
        String[] sortOptions = {"relevance", "price_asc", "price_desc", "rating_desc", "newest"};
        
        for (String sortBy : sortOptions) {
            ProductSearchRequest request = new ProductSearchRequest();
            request.setKeyword("iPhone");
            request.setPageNo(1);
            request.setPageSize(3);
            request.setSortBy(sortBy);

            ProductSearchResponse response = productSearchService.searchProducts(request);

            System.out.println("\n=== 排序测试: " + sortBy + " ===");
            System.out.println("结果数: " + response.getTotal());
            
            if (response.getProducts() != null) {
                response.getProducts().forEach(product -> {
                    System.out.println(String.format("商品: %s - $%.2f - 评分:%.1f", 
                        product.getTitle(), 
                        product.getPrice() != null ? product.getPrice() : 0.0,
                        product.getRating() != null ? product.getRating() : 0.0));
                });
            }
        }
    }

    @Test
    public void testEmptySearch() {
        // 空搜索测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setPageNo(1);
        request.setPageSize(10);

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 空搜索测试 ===");
        System.out.println("总结果数: " + response.getTotal());
        System.out.println("返回商品数: " + (response.getProducts() != null ? response.getProducts().size() : 0));
    }
}
