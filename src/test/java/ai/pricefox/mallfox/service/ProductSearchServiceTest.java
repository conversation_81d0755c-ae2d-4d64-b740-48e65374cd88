package ai.pricefox.mallfox.service;

import ai.pricefox.mallfox.model.param.ProductSearchRequest;
import ai.pricefox.mallfox.model.response.ProductSearchResponse;
import ai.pricefox.mallfox.service.product.ProductSearchService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 商品搜索功能测试
 * @since 2025/7/8
 */
@SpringBootTest
public class ProductSearchServiceTest {

    @Autowired
    private ProductSearchService productSearchService;

    @Test
    public void testBasicSearch() {
        // 基础搜索测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSortBy("relevance");

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("=== 基础搜索测试 ===");
        System.out.println("关键词: " + request.getKeyword());
        System.out.println("总结果数: " + response.getTotal());
        System.out.println("当前页商品数: " + response.getProducts().size());
        System.out.println("搜索耗时: " + response.getSearchStats().getSearchTime() + "ms");

        // 打印前几个商品
        response.getProducts().stream().limit(3).forEach(product -> {
            System.out.println(String.format("商品: %s - %s - $%.2f", 
                product.getBrand(), product.getTitle(), product.getPrice()));
        });
    }

    @Test
    public void testPriceRangeSearch() {
        // 价格区间搜索测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        request.setMinPrice(500.0);
        request.setMaxPrice(1200.0);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setSortBy("price_asc");

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 价格区间搜索测试 ===");
        System.out.println("价格区间: $" + request.getMinPrice() + " - $" + request.getMaxPrice());
        System.out.println("总结果数: " + response.getTotal());
        
        // 验证价格区间
        response.getProducts().forEach(product -> {
            System.out.println(String.format("商品: %s - $%.2f", 
                product.getTitle(), product.getPrice()));
        });
    }

    @Test
    public void testSpecFiltersSearch() {
        // 规格筛选测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");
        
        // 设置规格筛选 - 存储容量和颜色
        Map<String, List<String>> specFilters = Map.of(
            "1", List.of("128GB", "256GB"), // 存储容量
            "2", List.of("Black", "White")   // 颜色
        );
        //request.setSpecFilters(specFilters);
        
        request.setPageNo(1);
        request.setPageSize(10);

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 规格筛选搜索测试 ===");
        System.out.println("筛选条件: 存储容量(128GB,256GB), 颜色(Black,White)");
        System.out.println("总结果数: " + response.getTotal());
        
        response.getProducts().forEach(product -> {
            System.out.println(String.format("商品: %s - %s - %s - $%.2f", 
                product.getBrand(), product.getStorage(), product.getColor(), product.getPrice()));
        });
    }

    @Test
    public void testAvailableFilters() {
        // 可用筛选器测试
        ProductSearchRequest request = new ProductSearchRequest();
        request.setKeyword("iPhone");

        ProductSearchResponse response = productSearchService.searchProducts(request);

        System.out.println("\n=== 可用筛选器测试 ===");
        
        // 品牌筛选器
        System.out.println("可用品牌:");
        response.getAvailableFilters().getBrands().stream().limit(5).forEach(brand -> {
            System.out.println("  " + brand.getLabel() + " (" + brand.getCount() + ")");
        });

        // 规格筛选器
        System.out.println("\n可用规格筛选器:");
        response.getAvailableFilters().getSpecFilters().forEach(specFilter -> {
            System.out.println("  " + specFilter.getAttributeName() + ":");
            specFilter.getOptions().stream().limit(3).forEach(option -> {
                System.out.println("    " + option.getLabel() + " (" + option.getCount() + ")");
            });
        });

        // 价格统计
        if (response.getSearchStats().getPriceRange() != null) {
            System.out.println("\n价格统计:");
            System.out.println("  最低价: $" + response.getSearchStats().getPriceRange().getMinPrice());
            System.out.println("  最高价: $" + response.getSearchStats().getPriceRange().getMaxPrice());
            System.out.println("  平均价: $" + response.getSearchStats().getPriceRange().getAvgPrice());
        }
    }

    @Test
    public void testSortingOptions() {
        // 排序选项测试
        String[] sortOptions = {"relevance", "price_asc", "price_desc", "rating_desc", "newest"};
        
        for (String sortBy : sortOptions) {
            ProductSearchRequest request = new ProductSearchRequest();
            request.setKeyword("iPhone");
            request.setPageNo(1);
            request.setPageSize(5);
            request.setSortBy(sortBy);

            ProductSearchResponse response = productSearchService.searchProducts(request);

            System.out.println("\n=== 排序测试: " + sortBy + " ===");
            response.getProducts().forEach(product -> {
                System.out.println(String.format("商品: %s - $%.2f - 评分:%.1f", 
                    product.getTitle(), product.getPrice(), 
                    product.getRating() != null ? product.getRating() : 0.0));
            });
        }
    }
}
