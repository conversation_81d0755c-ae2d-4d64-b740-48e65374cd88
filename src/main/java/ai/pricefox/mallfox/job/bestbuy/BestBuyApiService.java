package ai.pricefox.mallfox.job.bestbuy;

import ai.pricefox.mallfox.config.BestBuyApiConfig;
import ai.pricefox.mallfox.convert.bestbuy.BestBuyConvert;
import ai.pricefox.mallfox.vo.bestbuy.BestBuyProductDetailRespVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * BestBuy API 服务类
 */
@Slf4j
@Component
public class BestBuyApiService {

    @Autowired
    private BestBuyApiConfig bestBuyApiConfig;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private BestBuyConvert bestBuyConvert;

    /**
     * 获取 BestBuy 产品详情
     * 
     * @param sku BestBuy 产品 SKU
     * @return BestBuy 产品详情
     */
    @Retryable(
            value = {RuntimeException.class, Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000)
    )
    public BestBuyProductDetailRespVO getProductDetail(String sku) {
        // 检查API密钥是否已配置
        if (!bestBuyApiConfig.isApiKeyConfigured()) {
            throw new RuntimeException("BestBuy API key is not configured");
        }

        // 构建 BestBuy API URL（包含show参数）
        String endpoint = bestBuyApiConfig.getProductDetailEndpoint(sku);
        String url = bestBuyApiConfig.buildProductDetailApiUrl(endpoint);

        log.info("构建的 BestBuy API URL: {}", url);

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            headers.set("Accept", "application/json");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<BestBuyProductDetailResponse> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, BestBuyProductDetailResponse.class);

            log.info("BestBuy API 响应状态: {}", response.getStatusCode());

            if (response.getBody() == null) {
                log.error("BestBuy API 返回空响应体，SKU: {}, 状态码: {}", sku, response.getStatusCode());
                throw new RuntimeException("Failed to fetch BestBuy product details for SKU: " + sku + ", empty response body");
            }

            log.info("成功获取 BestBuy 产品详情，SKU: {}, 响应数据: {}", sku, response.getBody());
            return bestBuyConvert.convertProductDetailResponseToRespVO(response.getBody());

        } catch (Exception e) {
            log.error("Error fetching BestBuy product details for SKU: {}", sku, e);
            throw new RuntimeException("Failed to fetch BestBuy product details", e);
        }
    }

    /**
     * 搜索产品
     *
     * @param query 搜索关键字
     * @param limit 返回数量限制
     * @param offset 偏移量
     * @return 搜索结果
     */
    @Retryable(
            value = {RuntimeException.class, Exception.class},
            maxAttempts = 3,
            backoff = @Backoff(delay = 1000)
    )
    public BestBuyProductSearchResponse searchProducts(String query, Integer limit, Integer offset) {
        // 检查API密钥是否已配置
        if (!bestBuyApiConfig.isApiKeyConfigured()) {
            throw new RuntimeException("BestBuy API key is not configured");
        }

        // 构建搜索URL（包含show参数）
        String endpoint = bestBuyApiConfig.getProductSearchEndpoint(query);
        String url = bestBuyApiConfig.buildProductSearchApiUrl(endpoint);
        
        // 添加分页参数
        if (limit != null) {
            url += "&pageSize=" + limit;
        }
        if (offset != null) {
            url += "&page=" + (offset / (limit != null ? limit : 10) + 1);
        }

        log.info("构建的 BestBuy 搜索 API URL: {}", url);

        try {
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            headers.set("Accept", "application/json");

            HttpEntity<String> entity = new HttpEntity<>(headers);

            // 发送请求
            ResponseEntity<BestBuyProductSearchResponse> response = restTemplate.exchange(
                    url, HttpMethod.GET, entity, BestBuyProductSearchResponse.class);
            
            if (response.getBody() == null) {
                throw new RuntimeException("Failed to search BestBuy products for query: " + query);
            }

            log.info("成功搜索 BestBuy 产品，查询: {}, 返回结果数: {}", 
                    query, response.getBody().getProducts() != null ? response.getBody().getProducts().size() : 0);
            return response.getBody();

        } catch (Exception e) {
            log.error("Error searching BestBuy products for query: {}", query, e);
            throw new RuntimeException("Failed to search BestBuy products", e);
        }
    }

    /**
     * 检查服务是否可用
     */
    public boolean isServiceAvailable() {
        return bestBuyApiConfig.isApiKeyConfigured() && !bestBuyApiConfig.isMockMode();
    }

    /**
     * 获取API配置信息
     */
    public String getApiInfo() {
        return String.format("BestBuy API - Base URL: %s, API Key Configured: %s, Mock Mode: %s",
                bestBuyApiConfig.getBaseUrl(),
                bestBuyApiConfig.isApiKeyConfigured(),
                bestBuyApiConfig.isMockMode());
    }
}
