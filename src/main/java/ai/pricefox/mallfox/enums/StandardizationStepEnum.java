package ai.pricefox.mallfox.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 标准化步骤枚举
 */
@Getter
@AllArgsConstructor
public enum StandardizationStepEnum {
    INITIALIZATION(1, "初始化"),
    BRAND_MATCHING(2, "品牌匹配"),
    CATEGORY_MATCHING(3, "品类匹配"),
    MODEL_MATCHING(4, "型号匹配"),
    COLOR_MATCHING(5, "颜色匹配"),
    STORAGE_MATCHING(6, "存储匹配"),
    REVIEW(7, "人工审核"),
    STANDARDIZATION(8, "标准化");

    private final Integer step;
    private final String description;

    public static StandardizationStepEnum fromStep(Integer step) {
        if (step == null) return null;
        for (StandardizationStepEnum value : values()) {
            if (value.getStep().equals(step)) {
                return value;
            }
        }
        // 添加容错处理
        if (step == 8) {
            return STANDARDIZATION;
        }
        return null;
    }

    public static boolean isValidStep(Integer step) {
        return fromStep(step) != null;
    }
}