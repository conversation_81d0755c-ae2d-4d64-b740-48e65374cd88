package ai.pricefox.mallfox.controller.admin.integration;

import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.convert.ebay.EbayConvert;
import ai.pricefox.mallfox.convert.ebay.EbayToProductDataConverter;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.job.ebay.EbayApiService;
import ai.pricefox.mallfox.job.ebay.EbayItemGroupResponse;
import ai.pricefox.mallfox.job.ebay.EbaySearchResponse;
import ai.pricefox.mallfox.model.dto.EbaySearchProgress;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.integration.EbaySyncService;
import ai.pricefox.mallfox.service.product.UnifiedProductDataService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.ebay.EbayItemDetailRespVO;
import ai.pricefox.mallfox.vo.ebay.EbayItemGroupRespVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchReqVO;
import ai.pricefox.mallfox.vo.ebay.EbaySearchRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * eBay API 控制器
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/ebay")
@Validated
@Tag(name = "管理后台-eBay管理", description = "eBay 商品信息接口")
public class EbayController {

    private final EbayApiService ebayApiService;
    private final CacheUtil cacheUtil;
    private final EbayToProductDataConverter ebayToProductDataConverter;
    private final UnifiedProductDataService unifiedProductDataService;
    private final EbayConvert ebayConvert;
    private final EbaySyncService ebaySyncService;

    /**
     * 获取 eBay 商品详情
     *
     * @param itemId eBay 商品 ID
     * @param syncData 是否同步数据到产品数据库
     * @return 商品详情
     */
    @GetMapping("/item/{itemId}")
    @Operation(summary = "获取 eBay 商品详情", description = "根据商品 ID 获取 eBay 商品的详细信息")
    public CommonResult<EbayItemDetailRespVO> getItemDetail(
            @Parameter(description = "eBay 商品 ID", example = "v1|1234567890|0")
            @PathVariable String itemId,
            @Parameter(description = "是否同步数据到产品数据库", example = "false")
            @RequestParam(defaultValue = "false") Boolean syncData) {

        try {
            log.info("获取 eBay 商品详情，itemId: {}, syncData: {}", itemId, syncData);

            EbayItemDetailRespVO respVO = ebayApiService.getItemDetail(itemId);

            // 如果需要同步数据，则调用数据同步服务
            if (syncData) {
                try {
                    log.info("开始同步 eBay 商品数据到产品数据库，itemId: {}", itemId);

                    // 转换为 ProductDataDTO
                    ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(respVO);

                    // 调用批量处理方法
                    unifiedProductDataService.batchProcessProductData(List.of(productDataDTO));

                    log.info("成功同步 eBay 商品数据，itemId: {}", itemId);
                } catch (Exception syncException) {
                    log.error("同步 eBay 商品数据失败，itemId: {}", itemId, syncException);
                    // 同步失败不影响主要功能，只记录错误
                }
            }

            log.info("成功获取 eBay 商品详情，itemId: {}", itemId);
            return CommonResult.success(respVO);

        } catch (Exception e) {
            log.error("获取 eBay 商品详情失败，itemId: {}", itemId, e);
            return CommonResult.error(500, "获取商品详情失败: " + e.getMessage());
        }
    }

    /**
     * 高级搜索商品 (POST) - 支持完整的 eBay Browse API 搜索功能
     *
     * @param searchRequest 增强的搜索请求参数
     * @return 搜索结果
     */
    @PostMapping("/search/advanced")
    @Operation(summary = "高级搜索 eBay 商品", description = "支持价格范围、品牌、条件、排序等完整的 eBay Browse API 搜索功能")
    public CommonResult<EbaySearchRespVO> searchItemsAdvanced(
            @RequestBody @Valid EbaySearchReqVO searchRequest) {

        try {
            log.info("开始高级搜索 eBay 商品，关键字: {}, 过滤条件: {}", 
                    searchRequest.getQuery(), searchRequest.buildFilterString());

            EbaySearchResponse searchResponse = ebayApiService.searchItemsAdvanced(searchRequest);
            EbaySearchRespVO respVO = ebayConvert.convertToSearchRespVO(searchResponse);

            log.info("高级搜索完成，关键字: {}, 返回 {} 条结果", 
                    searchRequest.getQuery(),
                    respVO.getItemSummaries() != null ? respVO.getItemSummaries().size() : 0);
            return CommonResult.success(respVO);

        } catch (Exception e) {
            log.error("高级搜索 eBay 商品失败，关键字: {}", searchRequest.getQuery(), e);
            return CommonResult.error(500, "高级搜索商品失败: " + e.getMessage());
        }
    }

//    @Scheduled(fixedRate = 1_800_000) // 每分钟执行一次
//    public void scheduledSyncItems() {
//        EbaySearchReqVO reqVO = new EbaySearchReqVO();
//        reqVO.setLimit(200);
//        reqVO.setSyncData(true);
//        reqVO.setQuery("");
//        reqVO.setModel("");
//        reqVO.setCategoryIds("9355");
//        reqVO.setAspectFilter("categoryId:9355");
//        reqVO.setIncludeSoldItems(false);
//        reqVO.setAvailableOnly(true);
//        reqVO.setExactMatch(true);
//
//        searchSyncItemsAdvanced(reqVO);
//    }

    @PostMapping("/search/sync")
    @Operation(summary = "高级搜索 eBay 商品-同步数据")
    public CommonResult<EbaySearchRespVO> searchSyncItemsAdvanced(@RequestBody @Valid EbaySearchReqVO searchRequest) {

        String progressKey = "search-product:ebay:progress";
        LocalDate today = LocalDate.now();

        // 获取缓存进度
        EbaySearchProgress progress = cacheUtil.getCacheObject(progressKey);
        if (progress == null) {
            progress = new EbaySearchProgress();
        }

        // 重置每日请求计数
        if (!today.equals(progress.getLastRequestDate())) {
            progress.setRequestCount(0);
            progress.setLastRequestDate(today);
        }

        // 检查请求次数限制
        if (progress.getRequestCount() >= 5000) {
            log.warn("已达每日请求上限，暂停同步");
            return CommonResult.error(429, "已达每日请求上限，明天继续");
        }

        // 设置 offset
        searchRequest.setOffset(progress.getOffset());

        try {
            while (true) {
                log.info("开始高级搜索 eBay 商品，关键字: {}, offset: {}",
                        searchRequest.getQuery(), progress.getOffset());

                EbaySearchResponse searchResponse = ebayApiService.searchItemsAdvanced(searchRequest);
                EbaySearchRespVO respVO = ebayConvert.convertToSearchRespVO(searchResponse);

                log.info("高级搜索完成，关键字:{}, 返回 {} 条结果",
                        searchRequest.getQuery(),
                        respVO.getItemSummaries() != null ? respVO.getItemSummaries().size() : 0);

                // 同步数据
                if (Boolean.TRUE.equals(searchRequest.getSyncData()) &&
                        searchResponse != null &&
                        searchResponse.getItemSummaries() != null &&
                        !searchResponse.getItemSummaries().isEmpty()) {
                    syncSearchResultsData(searchResponse.getItemSummaries());
                }

                // 更新进度
                progress.setOffset(searchRequest.getOffset() + searchRequest.getLimit());
                progress.setRequestCount(progress.getRequestCount() + 1);
                cacheUtil.setCacheObject(progressKey, progress);

                // 如果返回数据不足 200 条，说明拉取完成
                if (searchResponse.getItemSummaries().size() < 200) {
                    return CommonResult.success(respVO);
                }

                // 如果达到每日请求上限，提前退出
                if (progress.getRequestCount() >= 5000) {
                    log.warn("已达每日请求上限，保存进度并退出");
                    return CommonResult.success(respVO);
                }

                searchRequest.setOffset(progress.getOffset());
            }
        } catch (Exception e) {
            log.error("高级搜索 eBay 商品失败，关键字: {}", searchRequest.getQuery(), e);
            return CommonResult.error(500, "高级搜索商品失败: " + e.getMessage());
        }
    }


    /**
     * 获取商品项目组信息
     *
     * @param itemGroupId 商品组 ID
     * @return 商品项目组信息
     */
    @GetMapping("/item-group/{itemGroupId}")
    @Operation(summary = "获取 eBay 商品项目组", description = "根据商品组 ID 获取 eBay 商品项目组信息")
    public CommonResult<EbayItemGroupRespVO> getItemsByItemGroup(
            @Parameter(description = "商品组 ID", example = "1234567890", required = true)
            @PathVariable String itemGroupId,
            @Parameter(description = "是否同步数据到产品数据库", example = "false")
            @RequestParam(defaultValue = "false") Boolean syncData) {

        try {
            log.info("获取 eBay 商品项目组，itemGroupId: {}", itemGroupId);

            EbayItemGroupResponse itemGroupResponse = ebayApiService.getItemsByItemGroup(itemGroupId);
            EbayItemGroupRespVO respVO = ebayConvert.convertToItemGroupRespVO(itemGroupResponse);

            // 如果需要同步数据，则调用数据同步服务
            if (syncData) {
                respVO.getItems().forEach(item -> {
                    try {
                        log.info("开始同步 eBay 商品数据到产品数据库，itemId: {}", item.getItemId());

                        // 转换为 ProductDataDTO
                        ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(item);

                        // 调用批量处理方法
                        unifiedProductDataService.batchProcessProductData(List.of(productDataDTO));

                        log.info("成功同步 eBay 商品数据，itemId: {}", item.getItemId());
                    } catch (Exception syncException) {
                        log.error("同步 eBay 商品数据失败，itemId: {}", item.getItemId(), syncException);
                        // 同步失败不影响主要功能，只记录错误
                    }
                });
            }

            log.info("成功获取 eBay 商品项目组，itemGroupId: {}, 商品数量: {}",
                    itemGroupId, respVO.getItems() != null ? respVO.getItems().size() : 0);
            return CommonResult.success(respVO);

        } catch (Exception e) {
            log.error("获取 eBay 商品项目组失败，itemGroupId: {}", itemGroupId, e);
            return CommonResult.error(500, "获取商品项目组失败: " + e.getMessage());
        }
    }

    /**
     * 通过传统ID获取 eBay 商品详情
     *
     * @param legacyItemId 传统商品ID
     * @param legacyVariationId 传统变体ID（可选）
     * @param legacyVariationSku 传统变体SKU（可选）
     * @param fieldgroups 字段组（可选）
     * @param quantityForShippingEstimate 配送估算数量（可选）
     * @param syncData 是否同步数据到产品数据库
     * @return 商品详情
     */
    @GetMapping("/item/legacy/{legacyItemId}")
    @Operation(summary = "通过传统ID获取商品详情", description = "使用eBay传统商品ID获取商品详细信息")
    public CommonResult<EbayItemDetailRespVO> getItemByLegacyId(
            @Parameter(description = "传统商品ID", required = true, example = "110325312345")
            @PathVariable String legacyItemId,

            @Parameter(description = "传统变体ID", example = "123456789012")
            @RequestParam(required = false) String legacyVariationId,

            @Parameter(description = "传统变体SKU", example = "V123456789M")
            @RequestParam(required = false) String legacyVariationSku,

            @Parameter(description = "字段组，多个用逗号分隔", example = "PRODUCT,ADDITIONAL_SELLER_DETAILS")
            @RequestParam(required = false) String fieldgroups,

            @Parameter(description = "配送估算数量", example = "1")
            @RequestParam(required = false) Integer quantityForShippingEstimate,

            @Parameter(description = "是否同步数据到产品数据库", example = "false")
            @RequestParam(defaultValue = "false") Boolean syncData) {

        try {
            log.info("开始通过传统ID获取 eBay 商品详情，legacyItemId: {}", legacyItemId);

            // 参数验证
            if (legacyItemId == null || legacyItemId.trim().isEmpty()) {
                return CommonResult.error(400, "传统商品ID不能为空");
            }

            // 验证参数组合的有效性
            if (legacyVariationId != null && !legacyVariationId.trim().isEmpty() &&
                legacyVariationSku != null && !legacyVariationSku.trim().isEmpty()) {
                return CommonResult.error(400, "不能同时提供传统变体ID和传统变体SKU");
            }

            // 调用服务获取商品详情
            EbayItemDetailRespVO respVO = ebayApiService.getItemByLegacyId(
                    legacyItemId, legacyVariationId, legacyVariationSku,
                    fieldgroups, quantityForShippingEstimate);

            // 如果需要同步数据
            if (syncData && respVO != null) {
                try {
                    // 转换为 ProductDataDTO
                    ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(respVO);

                    // 调用批量处理方法
                    unifiedProductDataService.batchProcessProductData(List.of(productDataDTO));

                    log.info("成功同步 eBay 商品数据，legacyItemId: {}", legacyItemId);
                } catch (Exception syncException) {
                    log.error("同步 eBay 商品数据失败，legacyItemId: {}", legacyItemId, syncException);
                    // 同步失败不影响主要功能，只记录错误
                }
            }

            log.info("成功通过传统ID获取 eBay 商品详情，legacyItemId: {}", legacyItemId);
            return CommonResult.success(respVO);

        } catch (Exception e) {
            log.error("通过传统ID获取 eBay 商品详情失败，legacyItemId: {}", legacyItemId, e);
            return CommonResult.error(500, "获取商品详情失败: " + e.getMessage());
        }
    }

    /**
     * 一键同步api数据
     * 从 product_data_simplify 表中查询 data_channel={@link DataChannelEnum#CRAWLER} 的所有不重复 model，
     * 然后调用 eBay 搜索 API 进行数据同步
     * 支持断点续传：处理完的model会从缓存中移除，重新启动时从未处理的model继续
     *
     * @return 同步结果统计
     */
    @PostMapping("/sync/api-data")
    @Operation(summary = "一键同步api数据", description = "从爬虫数据中提取model，调用eBay API进行数据同步，支持断点续传")
    public CommonResult<EbaySyncService.SyncResultVO> oneClickSyncFromApiData() {
        try {
            log.info("开始执行一键同步api数据");
            return ebaySyncService.oneClickSyncFromApiData();
        } catch (Exception e) {
            log.error("一键同步api数据失败", e);
            return CommonResult.error(500, "同步失败: " + e.getMessage());
        }
    }

    /**
     * 清理同步缓存
     * 手动清理待处理model缓存，重置同步状态
     *
     * @return 清理结果
     */
    @DeleteMapping("/sync/cache")
    @Operation(summary = "清理同步缓存", description = "手动清理待处理model缓存，重置同步状态")
    public CommonResult<String> clearSyncCache() {
        try {
            log.info("开始清理同步缓存");
            return ebaySyncService.clearSyncCache();
        } catch (Exception e) {
            log.error("清理同步缓存失败", e);
            return CommonResult.error(500, "清理缓存失败: " + e.getMessage());
        }
    }

    /**
     * 查看同步缓存状态
     * 查看当前待处理的model数量和列表
     *
     * @return 缓存状态信息
     */
    @GetMapping("/sync/cache/status")
    @Operation(summary = "查看同步缓存状态", description = "查看当前待处理的model数量和列表")
    public CommonResult<EbaySyncService.SyncCacheStatusVO> getSyncCacheStatus() {
        try {
            log.info("查看同步缓存状态");
            return ebaySyncService.getSyncCacheStatus();
        } catch (Exception e) {
            log.error("查看同步缓存状态失败", e);
            return CommonResult.error(500, "查看缓存状态失败: " + e.getMessage());
        }
    }

    /**
     * 清空已同步商品缓存
     */
    @PostMapping("/sync/clear-synced-cache")
    @Operation(summary = "清空已同步商品缓存", description = "清空Redis中已同步商品的缓存，下次同步时会重新处理所有商品")
    public CommonResult<String> clearSyncedItemsCache() {
        try {
            ebaySyncService.clearSyncedItemsCache();
            return CommonResult.success("已同步商品缓存清空成功");
        } catch (Exception e) {
            log.error("清空已同步商品缓存失败", e);
            return CommonResult.error("清空缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取已同步商品数量
     */
    @GetMapping("/sync/synced-cache-stats")
    @Operation(summary = "获取已同步商品缓存统计", description = "获取Redis中已同步商品的数量统计")
    public CommonResult<Map<String, Object>> getSyncedCacheStats() {
        try {
            long syncedItemsCount = ebaySyncService.getSyncedItemsCount();

            Map<String, Object> stats = new HashMap<>();
            stats.put("syncedItemsCount", syncedItemsCount);
            stats.put("cacheKey", "ebay:sync:synced_items");
            stats.put("expireHours", 24);
            stats.put("description", "已同步商品缓存统计");

            return CommonResult.success(stats);
        } catch (Exception e) {
            log.error("获取已同步商品缓存统计失败", e);
            return CommonResult.error("获取缓存统计失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定商品是否在缓存中
     */
    @GetMapping("/sync/check-item/{itemId}")
    @Operation(summary = "检查商品是否已同步", description = "检查指定商品ID是否在已同步缓存中")
    public CommonResult<Map<String, Object>> checkItemInCache(
            @Parameter(description = "商品ID", required = true, example = "v1|1234567890|0")
            @PathVariable String itemId) {
        try {
            boolean inCache = ebaySyncService.checkItemInCache(itemId);

            Map<String, Object> result = new HashMap<>();
            result.put("itemId", itemId);
            result.put("inCache", inCache);
            result.put("status", inCache ? "已同步" : "未同步");
            result.put("description", inCache ? "该商品已在缓存中，同步时会跳过" : "该商品不在缓存中，同步时会处理");

            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("检查商品缓存状态失败，itemId: {}", itemId, e);
            return CommonResult.error("检查缓存状态失败: " + e.getMessage());
        }
    }

    /**
     * 同步搜索结果数据
     * 对搜索到的商品进行详情查询和数据同步，使用缓存避免重复同步
     *
     * @param itemSummaries 搜索结果商品列表
     */
    private void syncSearchResultsData(List<EbaySearchResponse.ItemSummary> itemSummaries) {
        if (itemSummaries == null || itemSummaries.isEmpty()) {
            return;
        }

        int totalItems = itemSummaries.size();
        int syncedItems = 0;
        int skippedItems = 0;
        int failedItems = 0;

        log.info("🔄 开始同步搜索结果数据，总商品数: {}", totalItems);
        List<ProductDataDTO> dtoList = new ArrayList<>();
        for (EbaySearchResponse.ItemSummary itemSummary : itemSummaries) {
            try {
                String itemId = itemSummary.getItemId();

                // 检查缓存中是否已经同步过该商品
                if (ebaySyncService.checkItemInCache(itemId)) {
                    log.debug("⏭️ 商品已同步过，跳过处理，itemId: {}", itemId);
                    skippedItems++;
                    continue;
                }

                // 获取商品详情
                log.debug("📡 获取商品详情，itemId: {}", itemId);
                EbayItemDetailRespVO itemDetailResponse = ebayApiService.getItemDetail(itemId);

                if (itemDetailResponse == null) {
                    log.warn("⚠️ 获取商品详情失败，itemId: {}", itemId);
                    failedItems++;
                    continue;
                }

                // 转换为VO
                ProductDataDTO productDataDTO = ebayToProductDataConverter.convertToProductDataDTO(itemDetailResponse);
                dtoList.add(productDataDTO);

                // 标记商品为已同步
                ebaySyncService.markItemAsSynced(itemId);

                syncedItems++;
                log.debug("✅ 商品同步完成，itemId: {}", itemId);

            } catch (Exception e) {
                log.error("❌ 同步商品数据失败，itemId: {}", itemSummary.getItemId(), e);
                failedItems++;
            }
        }

        unifiedProductDataService.batchProcessProductData(dtoList);

        log.info("🎉 搜索结果数据同步完成，总数: {}, 已同步: {}, 跳过: {}, 失败: {}",
                totalItems, syncedItems, skippedItems, failedItems);
    }

}
