package ai.pricefox.mallfox.controller.admin.product;

import ai.pricefox.mallfox.service.standard.StandardCategoryService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.*;
import cn.hutool.core.lang.tree.Tree;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 分类管理
 *
 * <AUTHOR>
 * @date 2025-05-18 12:19:29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/admin/v1/categories")
@Tag(name = "管理后台-分类管理", description = "分类管理相关接口")
public class CategoryController {

    private final StandardCategoryService standardCategoryService;

    // ==================== 分类管理 ====================

    /**
     * 创建分类
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    @Operation(summary = "创建分类", description = "创建新的分类")
    @PostMapping
    public CommonResult<CategoryInfoRespVO> createCategory(@Valid @RequestBody CategoryInfoCreateReqVO reqVO) {
        return standardCategoryService.createCategoryInfo(reqVO);
    }

    /**
     * 更新分类
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新分类", description = "根据ID更新分类信息")
    @PutMapping("/{id}")
    public CommonResult<CategoryInfoRespVO> updateCategory(@Valid @RequestBody CategoryInfoUpdateReqVO reqVO) {
        return standardCategoryService.updateCategoryInfo(reqVO);
    }

    /**
     * 根据ID获取分类
     *
     * @param id 分类ID
     * @return 分类信息
     */
    @Operation(summary = "获取分类详情", description = "根据ID获取分类详细信息")
    @GetMapping("/{id}")
    public CommonResult<CategoryInfoRespVO> getCategoryById(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id) {
        return standardCategoryService.getCategoryInfoById(id);
    }

    /**
     * 分页查询分类
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    @Operation(summary = "分页查询分类", description = "分页查询分类列表")
    @GetMapping
    public CommonResult<PageResult<CategoryInfoRespVO>> getCategoryPage(CategoryInfoPageReqVO reqVO) {
        return standardCategoryService.getCategoryInfoPage(reqVO);
    }

    /**
     * 获取所有分类列表
     *
     * @return 分类列表
     */
    @Operation(summary = "获取所有分类列表", description = "获取所有分类列表，无需分页")
    @GetMapping("/list")
    public CommonResult<List<CategoryInfoRespVO>> getAllCategories() {
        return standardCategoryService.getAllCategoryInfos();
    }

    /**
     * 获取分类树形结构
     *
     * @return 分类树
     */
    @Operation(summary = "获取分类树", description = "获取分类树形结构列表")
    @GetMapping("/tree")
    public CommonResult<List<Tree<Long>>> getCategoryTree() {
        return standardCategoryService.getCategoryInfoTree();
    }

    /**
     * 根据父分类ID获取子分类列表
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    @Operation(summary = "根据父分类ID获取子分类", description = "根据父分类ID获取子分类列表")
    @GetMapping("/parent/{parentId}")
    public CommonResult<List<CategoryInfoRespVO>> getCategoriesByParentId(
            @Parameter(description = "父分类ID", example = "0") @PathVariable Long parentId) {
        return standardCategoryService.getCategoryInfosByParentId(parentId);
    }

    /**
     * 删除分类
     *
     * @param id 分类ID
     * @return 删除结果
     */
    @Operation(summary = "删除分类", description = "根据ID删除分类")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> deleteCategory(
            @Parameter(description = "分类ID", example = "1") @PathVariable Long id) {
        return standardCategoryService.deleteCategoryInfo(id);
    }
}
