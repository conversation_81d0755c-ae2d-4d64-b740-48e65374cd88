package ai.pricefox.mallfox.controller;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.StandardizationResultDTO;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import ai.pricefox.mallfox.service.rules.StandardizationResultService;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据标准化控制器
 * 用于测试和调用数据标准化服务
 */
@Slf4j
@AllArgsConstructor
@RestController
@RequestMapping("/api/standardization")
public class DataStandardizationTestController {

    private final DataStandardizationService dataStandardizationService;
    private final StandardizationResultService standardizationResultService;

    // 注入已存在的KieContainer
    private final KieContainer kieContainer;

    /**
     * 标准化BestBuy商品数据（带跟踪）
     *
     * @return 标准化结果
     */
    @GetMapping("/bestBuyApiWithTracking")
    public Map<String, Object> standardizeBestBuyProductWithTracking() {
        log.info("=== 开始标准化BestBuy商品数据（带跟踪） ===");

        try {
            // 创建BestBuy测试数据
            ProductDataDTO originalData = createBestBuyTestData();

            // 执行带跟踪的标准化处理
            DynamicStandardProduct standardProduct = dataStandardizationService.standardizeProductData(originalData);

            // 处理标准化结果
            StandardizationResultDTO standardizationResult = standardizationResultService.processStandardizationResult(originalData, standardProduct);
            standardizationResult.setOriginalData(originalData);
            // 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "数据标准化完成（带跟踪）");
            result.put("standardizationResult", standardizationResult);

            log.info("\n=== 标准化完成 ===");
            log.info("总计字段数: {}", standardizationResult.getTotalFieldsCount());
            log.info("已匹配字段数: {}", standardizationResult.getMatchedFieldsCount());
            log.info("未匹配字段数: {}", standardizationResult.getUnmatchedFieldsCount());
            log.info("空值字段数: {}", standardizationResult.getEmptyFieldsCount());

            return result;

        } catch (Exception e) {
            log.error("数据标准化过程中发生错误: {}", e.getMessage());
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("success", false);
            errorResult.put("message", "数据标准化失败: " + e.getMessage());
            return errorResult;
        }
    }


    /**
     * 创建BestBuy测试数据
     *
     * @return ProductDataDTO对象
     */
    private ProductDataDTO createBestBuyTestData() {
        ProductDataDTO productData = new ProductDataDTO();
        productData.setSourcePlatform(ProductPlatformEnum.BESTBUY);
        productData.setDataChannel(DataChannelEnum.API);
        productData.setPlatformSkuId("6525428");
        productData.setTitle("Apple - iPhone 15 Pro Max 512GB - Apple Intelligence - Blue Titanium (AT&T)");
        productData.setBrand("Apple");
        productData.setModel("MU6E3LL/A");
        productData.setColor("Blue Titanium");
        productData.setStorage("512GB");
        productData.setUpcCode("************");
        productData.setCondition("New");
        productData.setManufacturer("Apple");
        productData.setProcessor("A17 Pro");
        productData.setScreenSize("6.7\"");
        productData.setCellularTechnology("5G");
        productData.setOperatingSystem("iOS");
        productData.setBatteryPower("4422 mAh");
        productData.setItemWeight("7.81 oz");
        productData.setDimensions("6.29 x 3.02 x 0.32 in");
        productData.setWarrantyLabor("1 year");
        productData.setWarrantyParts("1 year");
        productData.setSeries("iPhone 15 Pro Max");
        productData.setCategoryLevel1("Cell Phones");
        productData.setCategoryLevel2("iPhone");
        productData.setCategoryLevel3("All iPhone");
        return productData;
    }

    /**
     * 调试Drools配置
     *
     * @return Drools配置信息
     */
    @GetMapping("/debug")
    public Map<String, Object> debugDrools() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用已注入的KieContainer而不是重新创建
            result.put("kieContainer", "已成功注入");
            result.put("kieBaseNames", kieContainer.getKieBaseNames());

            // 检查是否有可用的KieBase
            if (kieContainer.getKieBaseNames().isEmpty()) {
                result.put("warning", "没有加载任何规则，请检查配置");
                return result;
            }

            for (String kbaseName : kieContainer.getKieBaseNames()) {
                result.put("kieSessionNamesIn_" + kbaseName, kieContainer.getKieSessionNamesInKieBase(kbaseName));
            }

            // 尝试创建KieSession
            try {
                KieSession kieSession = kieContainer.newKieSession("ksession-rules");
                result.put("kieSession", "命名会话创建成功");
                if (kieSession != null) {
                    kieSession.dispose();
                }
            } catch (Exception e) {
                result.put("kieSessionError", "命名会话创建失败: " + e.getMessage());
                try {
                    KieSession kieSession = kieContainer.newKieSession();
                    result.put("defaultKieSession", "默认会话创建成功");
                    if (kieSession != null) {
                        kieSession.dispose();
                    }
                } catch (Exception ex) {
                    result.put("defaultKieSessionError", "默认会话创建失败: " + ex.getMessage());
                }
            }
        } catch (Exception e) {
            result.put("error", e.getMessage());
        }

        return result;
    }

}
