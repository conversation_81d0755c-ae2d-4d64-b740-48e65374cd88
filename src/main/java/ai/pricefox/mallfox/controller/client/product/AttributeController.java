package ai.pricefox.mallfox.controller.client.product;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.product.ProductAttribute;
import ai.pricefox.mallfox.model.param.ProductAttributeParam;
import ai.pricefox.mallfox.service.product.ProductAttributeService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 规格相关
 *
 * <AUTHOR>
 * @date 2025-05-18 12:19:29
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/v1/attributes")
@Tag(name = "客户端-规格管理", description = "规格管理")
public class AttributeController {

    private final ProductAttributeService productAttributeService;

    /**
     * 分页列表
     *
     * @param page             分页对象
     * @param productAttribute 规格
     * @return
     */
    @Operation(summary = "分页查询规格", description = "分页查询规格列表")
    @GetMapping("/page")
    public CommonResult<PageResult<ProductAttribute>> getPage(Page page, ProductAttribute productAttribute) {
        Page<ProductAttribute> attributePage = productAttributeService.page(page, Wrappers.query(productAttribute));

        PageResult<ProductAttribute> pageResult = new PageResult<>();
        pageResult.setTotal(attributePage.getTotal());
        pageResult.setList(attributePage.getRecords());

        return CommonResult.success(pageResult);
    }

    /**
     * 规格查询
     *
     * @param id
     * @return CommonResult
     */
    @Operation(summary = "获取规格详情", description = "根据ID获取规格详细信息")
    @GetMapping("/{id}")
    public CommonResult<ProductAttributeParam> getById(@Parameter(description = "规格ID", example = "1") @PathVariable("id") String id) {
        ProductAttributeParam attribute = productAttributeService.getProductAttributeById(id);
        if (attribute == null) {
            throw exception(ErrorCodeConstants.ATTRIBUTE_NOT_EXIST);
        }
        return CommonResult.success(attribute);
    }

    /**
     * 规格新增
     *
     * @param productAttribute 规格
     * @return CommonResult
     */
    @Operation(summary = "创建规格", description = "创建新的规格")
    @PostMapping
    public CommonResult<Boolean> save(@RequestBody ProductAttributeParam productAttribute) {

        return CommonResult.success(productAttributeService.saveProductAttribute(productAttribute));
    }

    /**
     * 规格修改
     *
     * @param productAttribute 规格
     * @return CommonResult
     */
    @Operation(summary = "更新规格", description = "更新规格信息")
    @PutMapping
    public CommonResult<Boolean> updateById(@RequestBody ProductAttributeParam productAttribute) {
        return CommonResult.success(productAttributeService.saveProductAttribute(productAttribute));
    }

    /**
     * 规格删除
     *
     * @param id
     * @return CommonResult
     */
    @Operation(summary = "删除规格", description = "根据ID删除规格")
    @DeleteMapping("/{id}")
    public CommonResult<Boolean> removeById(@Parameter(description = "规格ID", example = "1") @PathVariable String id) {
        boolean result = productAttributeService.removeById(id);
        return CommonResult.success(result);
    }

}
