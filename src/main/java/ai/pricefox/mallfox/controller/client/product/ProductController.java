package ai.pricefox.mallfox.controller.client.product;

import ai.pricefox.mallfox.model.param.HomepageProductRequest;
import ai.pricefox.mallfox.model.param.ProductHistoryRequest;
import ai.pricefox.mallfox.model.param.ProductPageRequest;
import ai.pricefox.mallfox.model.param.ProductSearchRequest;
import ai.pricefox.mallfox.model.response.*;
import ai.pricefox.mallfox.service.product.HomepageService;
import ai.pricefox.mallfox.service.product.PriceHistoryService;
import ai.pricefox.mallfox.service.product.ProductSearchService;
import ai.pricefox.mallfox.service.product.impl.MockProductService;
import ai.pricefox.mallfox.service.product.ProductDetailService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.product.ProductDetailRespVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 商品相关
 *
 * <AUTHOR>
 * @date 2025-05-18 12:19:29
 */
@Slf4j
@RestController
@RequestMapping("/v1/products")
@Tag(name = "客户端-门户-商品管理", description = "商品管理")
public class ProductController {

    @Autowired
    private MockProductService mockProductService;

    @Autowired
    private HomepageService homepageService;

    @Autowired
    private ProductSearchService productSearchService;

    @Autowired
    private PriceHistoryService priceHistoryService;

    @Autowired
    private ProductDetailService productDetailService;


    /**
     * 获取首页模块的商品列表
     *
     * @param request
     * @return 首页商品卡片列表
     */
    @GetMapping("/homePage")
    public CommonResult<List<ProductCardDTO>> getHomepageProducts(HomepageProductRequest request) {
        List<ProductCardDTO> data = homepageService.getProductListForHomepage(request);
        return CommonResult.success(data);
    }


    /**
     * 商品搜索与筛选接口
     *
     * @param request
     * @return
     */
    @PostMapping("/search")
    @Operation(summary = "商品搜索与筛选接口", description = "商品列表页的搜索核心接口")
    public PageResult<ProductSearchItemDTO> search(@RequestBody ProductSearchRequest request) {
        ProductSearchResponse response = productSearchService.searchProducts(request);
        if (Objects.isNull(response)) {
            return PageResult.empty();
        }
        return PageResult.of(response.getProducts(), response.getTotal());
    }

    /**
     * 获取商品价格历史
     *
     * @param request
     * @return
     */
    @PostMapping("/price-history")
    @Operation(summary = "获取商品价格历史", description = "获取商品价格历史")
    public CommonResult<PriceHistoryResponseDTO> getPriceHistory(@RequestBody ProductHistoryRequest request) {
        PriceHistoryResponseDTO priceHistory = priceHistoryService.getPriceHistory(request.getSkuCode(), request.getPeriod(), request.getPlatforms());
        return CommonResult.success(priceHistory);
    }


    /**
     * 获取Mock商品分页
     *
     * @param request 请求参数
     * @return
     */
    @GetMapping("/mockPage")
    public PageResult<ProductPageResponse> getMockPage(ProductPageRequest request) {
        return mockProductService.getMockProductPage(request.getKeyWord(), request.getPageNo(), request.getPageSize());
    }

    /**
     * 获取Mock专家评论
     *
     * @param request 请求参数
     * @return 专家评论分页数据
     */
    @Operation(summary = "获取专家评论分页列表", description = "根据关键词查询专家评论，支持按最新、热门、推荐等筛选 latest, popular, recommended")
    @GetMapping("/mockExpertReview")
    public PageResult<ExpertReviewResponse> getMockExpertReviewPage(ProductPageRequest request) {
        return mockProductService.getMockExpertPage(request.getKeyWord(), request.getPageNo(), request.getPageSize());
    }

    /**
     * 获取商品详情
     *
     * @param skuCode SKU编码
     * @return 商品详情
     */
    @Operation(summary = "获取商品详情", description = "根据sku编码获取商品详情及变体信息")
    @GetMapping("/detail")
    public CommonResult<ProductDetailRespVO> getProductDetail(
            @Parameter(description = "SKU编码", example = "SKU001", required = true)
            @RequestParam String skuCode) {

        log.info("获取商品详情请求: skuCode={}", skuCode);

        try {
            ProductDetailRespVO productDetail = productDetailService.getProductDetail(skuCode);

            if (productDetail == null) {
                log.warn("商品详情不存在: skuCode={}", skuCode);
                return CommonResult.error("商品详情不存在");
            }

            return CommonResult.success(productDetail);

        } catch (Exception e) {
            log.error("获取商品详情失败: skuCode={}, error={}",
                skuCode, e.getMessage(), e);
            return CommonResult.error("获取商品详情失败");
        }
    }


}
