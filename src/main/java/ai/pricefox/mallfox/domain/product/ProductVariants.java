package ai.pricefox.mallfox.domain.product;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品变体信息实体类
 * 对应 product_variants 表
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@TableName("product_variants")
public class ProductVariants {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.INPUT)
    private String id;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private LocalDateTime createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    private LocalDateTime updateDate;

    /**
     * 变体ID
     */
    @TableField("unique_id")
    private String uniqueId;

    /**
     * 产品spu code
     */
    @TableField("spu_code")
    private String spuCode;

    /**
     * 图片ID
     */
    @TableField("image_id")
    private Integer imageId;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 属性json - 包含color, storage, condition等所有变体属性
     */
    @TableField("option_json")
    private String optionJson;

    /**
     * 排序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 价格 usd
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * sku code,自建唯一
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * sku
     */
    @TableField("sku")
    private String sku;

    /**
     * 条形码
     */
    @TableField("barcode")
    private String barcode;

    /**
     * 提示
     */
    @TableField("note")
    private String note;

    /**
     * 库存
     */
    @TableField("inventory_quantity")
    private Integer inventoryQuantity;

    /**
     * 重量
     */
    @TableField("weight")
    private BigDecimal weight;

    /**
     * 重量单位
     */
    @TableField("weight_unit")
    private BigDecimal weightUnit;

    /**
     * 成本价
     */
    @TableField("cost_price")
    private BigDecimal costPrice;

    /**
     * 采购价 (对应原来的listPrice)
     */
    @TableField("procure_price")
    private BigDecimal procurePrice;

    /**
     * 高
     */
    @TableField("height")
    private BigDecimal height;

    /**
     * 长
     */
    @TableField("length")
    private BigDecimal length;

    /**
     * 宽
     */
    @TableField("width")
    private BigDecimal width;

    /**
     * 备注
     */
    @TableField("remarks")
    private String remarks;
}