package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 变更日志
 */
@Data
@TableName("change_log")
public class ChangeLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String productIdentifier;
    private String changeType;
    private String changeDescription;
    private String operatorName;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
}