package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

// 最终商品实体
@TableName("final_product")
@Data
public class FinalProduct {
    @TableId(type = IdType.AUTO)
    private Long id;
    private String skuId;
    private String standardData;
    private String status;  // 状态（DRAFT/REVIEW/RELEASED）
    private LocalDateTime reviewTime;
    private String reviewer;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}