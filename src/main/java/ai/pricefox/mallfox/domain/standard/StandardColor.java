package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 标准颜色库
 */
@TableName(value ="standard_color")
@Data
public class StandardColor implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * code1
     */
    private String codeCodeLevel1;

    /**
     * code2
     */
    private String codeCodeLevel2;

    /**
     * 名称1
     */
    private String codeNameLevel1;

    /**
     * 名称2
     */
    private String codeNameLevel2;
}