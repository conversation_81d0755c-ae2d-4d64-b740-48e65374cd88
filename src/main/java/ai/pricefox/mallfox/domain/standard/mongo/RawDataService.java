package ai.pricefox.mallfox.domain.standard.mongo;

import ai.pricefox.mallfox.repository.mongo.RawDataRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

// 数据存储服务
@Service
@RequiredArgsConstructor
public class RawDataService {
    private final RawDataRepository rawDataRepository;

    /**
     * 保存原始数据
     *
     * @param platformCode      平台代码
     * @param productIdentifier 商品标识
     * @param dataJson          数据JSON
     * @return 保存后的数据ID
     */
    public String saveRawData(String platformCode, String productIdentifier, String dataJson) {
        RawData rawData = new RawData();
        rawData.setPlatformCode(platformCode);
        rawData.setProductIdentifier(productIdentifier);
        rawData.setDataJson(dataJson);
        rawData.setCreateTime(new Date());
        return rawDataRepository.save(rawData).getId();
    }
}
