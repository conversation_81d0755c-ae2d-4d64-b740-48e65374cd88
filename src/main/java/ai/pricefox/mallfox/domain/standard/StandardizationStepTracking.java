package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.FieldMappingStatusEnum;
import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 标准化步骤跟踪
 */
@Data
@TableName("standardization_step_tracking")
public class StandardizationStepTracking {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
    private String createUsername;
    private String updateUsername;
    /**
     * 产品标识符
     */
    private String productIdentifier;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 当前步骤
     */
    private StandardizationStepEnum currentStep;
    /**
     * 步骤状态
     */
    private FieldMappingStatusEnum stepStatus;
    /**
     * 类别匹配状态
     */
    private String categoryMatchStatus;
    /**
     * 品牌匹配状态
     */
    private String brandMatchStatus;
    /**
     * 型号匹配状态
     */
    private String modelMatchStatus;
    /**
     * 规格匹配状态
     */
    private String specificationMatchStatus;
    /**
     * 合规匹配状态
     */
    private String complianceMatchStatus;
    /**
     * 是否完成
     */
    private Boolean isCompleted;

}