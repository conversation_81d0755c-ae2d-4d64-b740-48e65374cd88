package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.FieldMappingStatusEnum;
import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 标准化中间数据
 */
@Data
@TableName("standardization_intermediate_data")
public class StandardizationIntermediateData {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateDate;
    private String createUsername;
    private String updateUsername;
    /**
     * 商品标识符
     */
    private String productIdentifier;
    /**
     * 平台编码
     */
    private String platformCode;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 数据渠道
     */
    private String dataChannel;
    /**
     * 步骤
     */
    private StandardizationStepEnum step;
    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 源数据
     */
    private String originalData;
    /**
     * 匹配后数据
     */
    private String matchedValue;
    /**
     * 匹配状态
     */
    private FieldMappingStatusEnum matchStatus;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作人备注
     */
    private String operationNotes;

}