package ai.pricefox.mallfox.domain.standard.mongo;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

// 创建Mongo实体类
@Data
@Document(collection = "raw_data")
public class RawData {
    @Id
    private String id;
    /**
     * 平台编码
     */
    private String platformCode;
    /**
     * 渠道编码
     */
    private DataChannelEnum dataChannelEnum;
    /**
     * 商品标识符
     */
    private String productIdentifier;
    /**
     * 商品数据
     */
    private String dataJson;
    /**
     * 创建时间
     */
    private Date createTime;
}