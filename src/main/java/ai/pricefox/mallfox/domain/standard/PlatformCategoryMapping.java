package ai.pricefox.mallfox.domain.standard;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 平台与标准品类映射规则
 */
@TableName(value ="platform_category_mapping")
@Data
public class PlatformCategoryMapping implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 修改时间
     */
    private Date updateDate;

    /**
     * 创建人名称
     */
    private String createUsername;

    /**
     * 更新人名称
     */
    private String updateUsername;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 标准库品类编码
     */
    private String standardCategoryCode;

    /**
     * 标准库品类名称
     */
    private String standardCategoryName;

    /**
     * 标准库类目级别
     */
    private Integer standardLevel;

    /**
     * 第三方平台品类名称
     */
    private String platformCategoryName;

    /**
     * 第三方平台品类级别
     */
    private Integer platformLevel;
}