package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

// 标准化记录实体
@TableName("tripartite_standardization_record")
@Data
public class StandardizationRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 创建人
     */
    private String createUsername;
    /**
     * 更新时间
     */
    private Date updateDate;
    /**
     * 更新人
     */
    private String updateUsername;
    /**
     * 平台编码
     */
    private String platformCode;
    /**
     * 数据ID
     */
    private String dataId;
    /**
     * SKU ID
     */
    private String skuId;
    /**
     * 步骤类型
     */
    private StandardizationStepEnum stepType;  // 步骤类型（MERGE/CATEGORY/BRAND/SPEC）
    /**
     * 修改前数据
     */
    private String beforeData;  // 标准化前数据
    /**
     * 修改后数据
     */
    private String afterData;  // 标准化后数据
    /**
     * 变更详情
     */
    private String changeDetails;  // 变更详情
    /**
     * 操作者
     */
    private String operator;  // 操作者（系统或人工）

}