package ai.pricefox.mallfox.domain.standard;

import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.util.Date;

/**
 * 标准化操作日志
 */
@Data
@TableName("standardization_operation_log")
public class StandardizationOperationLog {
    @TableId(type = IdType.AUTO)
    private Long id;
    @TableField(fill = FieldFill.INSERT)
    private Date createDate;
    /**
     * 产品标识符
     */
    private String productIdentifier;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 步骤
     */
    private StandardizationStepEnum step;
    /**
     * 字段名称
     */
    private String fieldName;
    /**
     * 修改前的值
     */
    private String beforeValue;
    /**
     * 修改后的值
     */
    private String afterValue;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作人备注
     */
    private String operationNotes;

}