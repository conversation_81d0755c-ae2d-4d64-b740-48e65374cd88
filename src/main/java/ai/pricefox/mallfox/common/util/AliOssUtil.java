package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.config.AliyunOssConfig;
import cn.hutool.core.util.IdUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 阿里云 OSS 文件上传工具类（支持目录 + 批量上传）
 */
@Component
public class AliOssUtil {

    private final AliyunOssConfig aliOssProperties;

    @Autowired
    public AliOssUtil(AliyunOssConfig aliOssProperties) {
        this.aliOssProperties = aliOssProperties;
    }

    /**
     * 上传单个文件到阿里云OSS
     *
     * @param file  待上传的文件
     * @param dirPath 上传的目标目录路径（可为 null 或空字符串表示根目录）
     * @return 上传成功后文件的访问URL
     * @throws IOException 如果文件输入流读取失败
     */
    public String upload(MultipartFile file, String dirPath) throws IOException {
        List<String> urls = batchUpload(new MultipartFile[]{file}, dirPath);
        return urls.isEmpty() ? null : urls.get(0);
    }

    /**
     * 批量上传文件到阿里云OSS，并支持指定目录
     *
     * @param files   要上传的文件数组
     * @param dirPath 上传的目标目录路径（可为 null 或 "" 表示根目录）
     * @return 上传成功后的文件URL列表
     * @throws IOException 如果文件输入流读取失败
     */
    public List<String> batchUpload(MultipartFile[] files, String dirPath) throws IOException {
        if (files == null || files.length == 0) {
            return Collections.emptyList();
        }

        // 初始化 OSSClient（整个方法中只创建一次）
        String endpoint = aliOssProperties.getEndpoint();
        String accessKeyId = aliOssProperties.getAccessKeyId();
        String accessKeySecret = aliOssProperties.getAccessKeySecret();
        String bucketName = aliOssProperties.getBucketName();

        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);

        try {
            return Arrays.stream(files)
                    .map(file -> uploadFileToOSS(ossClient, file, bucketName, dirPath))
                    .collect(Collectors.toList());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

    /**
     * 单个文件上传逻辑封装
     */
    private String uploadFileToOSS(OSS ossClient, MultipartFile file, String bucketName, String dirPath) {
        try (InputStream inputStream = file.getInputStream()) {
            String originalFilename = file.getOriginalFilename();
            String fileExtension = "";

            if (originalFilename != null && originalFilename.lastIndexOf('.') > 0) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf('.'));
            }

            String objectName = IdUtil.fastSimpleUUID() + fileExtension;

            // 拼接完整路径：dirPath + "/" + filename.ext
            if (dirPath != null && !dirPath.endsWith("/")) {
                dirPath += "/";
            }
            String fullObjectName = (dirPath != null ? dirPath : "") + objectName;

            ossClient.putObject(bucketName, fullObjectName, inputStream);

            return "https://" + bucketName + "." + aliOssProperties.getEndpoint() + "/" + fullObjectName;

        } catch (IOException e) {
            throw new RuntimeException("上传文件 [" + file.getOriginalFilename() + "] 到 OSS 失败", e);
        }
    }
}
