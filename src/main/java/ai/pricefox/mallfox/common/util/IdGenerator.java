package ai.pricefox.mallfox.common.util;

import cn.hutool.core.util.IdUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @desc ID生成器
 * @since 2025/6/21
 */
public class IdGenerator {

    private static final Logger log = LoggerFactory.getLogger(IdGenerator.class);

    /**
     * 生成SKU
     **/
    public static String generateSku() {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        nextIdStr = nextIdStr.length() > 11 ? nextIdStr.substring(nextIdStr.length() - 11) : nextIdStr;
        return "PK" + nextIdStr;
    }

    /**
     * 生成SPU
     **/
    public static String generateSpu() {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        nextIdStr = nextIdStr.length() > 8 ? nextIdStr.substring(nextIdStr.length() - 8) : nextIdStr;

        return "PP" + nextIdStr;
    }

    /**
     * 生成评论ID
     **/
    public static String generateReviewId() {
        String nextIdStr = IdUtil.getSnowflakeNextIdStr();
        nextIdStr = nextIdStr.length() > 13 ? nextIdStr.substring(nextIdStr.length() - 13) : nextIdStr;
        return "PR" + nextIdStr;
    }

    /**
     * 生成订单号
     **/
    public String generateOrderNo() {
        return "PO" + IdUtil.fastSimpleUUID();
    }

    // 提供一个更通用的方法
    public String generate(String prefix) {
        return prefix + IdUtil.fastSimpleUUID();
    }

    public static void main(String[] args) {
        IdGenerator idGenerator = new IdGenerator();
        String snowflakeNextIdStr = IdUtil.getSnowflakeNextIdStr();
        log.info("雪花算法生成的ID: {}", snowflakeNextIdStr);

        log.info("\n--- 批量生成10个订单号 ---");
        for (int i = 0; i < 10; i++) {
            log.info("");
        }
    }
}