package ai.pricefox.mallfox.common.util;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 缓存工具类
 * 提供统一的缓存操作方法，使用 RedisKeyConstants 中定义的键和过期时间
 * 
 * <AUTHOR>
 * @since 2025-07-15
 */
@Slf4j
@Component
public class CacheUtil {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    // ========== 用户模块缓存 ==========

    /**
     * 设置用户令牌缓存
     */
    public void setUserToken(String token, Long userId) {
        String key = RedisKeyConstants.USER_TOKEN + token;
        redisTemplate.opsForValue().set(key, userId, RedisKeyConstants.USER_TOKEN_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取用户令牌缓存
     */
    public Long getUserToken(String token) {
        String key = RedisKeyConstants.USER_TOKEN + token;
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? (Long) value : null;
    }

    /**
     * 删除用户令牌缓存
     */
    public void deleteUserToken(String token) {
        String key = RedisKeyConstants.USER_TOKEN + token;
        redisTemplate.delete(key);
    }

    /**
     * 设置用户收藏列表缓存
     */
    public void setUserWishlist(Long userId, Object wishlist) {
        String key = RedisKeyConstants.USER_WISHLIST + userId;
        redisTemplate.opsForValue().set(key, wishlist, RedisKeyConstants.USER_WISHLIST_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取用户收藏列表缓存
     */
    public Object getUserWishlist(Long userId) {
        String key = RedisKeyConstants.USER_WISHLIST + userId;
        return redisTemplate.opsForValue().get(key);
    }

    // ========== 商品模块缓存 ==========

    /**
     * 设置商品详情缓存
     */
    public void setProductDetail(String skuId, String sourcePlatform, Object productDetail) {
        String key = RedisKeyConstants.PRODUCT_DETAIL + skuId + "_" + sourcePlatform;
        redisTemplate.opsForValue().set(key, productDetail, RedisKeyConstants.PRODUCT_DETAIL_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取商品详情缓存
     */
    public Object getProductDetail(String skuId, String sourcePlatform) {
        String key = RedisKeyConstants.PRODUCT_DETAIL + skuId + "_" + sourcePlatform;
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除商品详情缓存
     */
    public void deleteProductDetail(String skuId, String sourcePlatform) {
        String key = RedisKeyConstants.PRODUCT_DETAIL + skuId + "_" + sourcePlatform;
        redisTemplate.delete(key);
    }

    /**
     * 设置商品标记缓存 - Offers
     */
    public void setProductCalibrationOffers(Long offerId, Object tags) {
        String key = RedisKeyConstants.PRODUCT_CALIBRATION_OFFERS + offerId;
        redisTemplate.opsForValue().set(key, tags, RedisKeyConstants.PRODUCT_CALIBRATION_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取商品标记缓存 - Offers
     */
    public Object getProductCalibrationOffers(Long offerId) {
        String key = RedisKeyConstants.PRODUCT_CALIBRATION_OFFERS + offerId;
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 设置商品标记缓存 - Simplify
     */
    public void setProductCalibrationSimplify(Long simplifyId, Object tags) {
        String key = RedisKeyConstants.PRODUCT_CALIBRATION_SIMPLIFY + simplifyId;
        redisTemplate.opsForValue().set(key, tags, RedisKeyConstants.PRODUCT_CALIBRATION_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取商品标记缓存 - Simplify
     */
    public Object getProductCalibrationSimplify(Long simplifyId) {
        String key = RedisKeyConstants.PRODUCT_CALIBRATION_SIMPLIFY + simplifyId;
        return redisTemplate.opsForValue().get(key);
    }

    // ========== 字段追踪模块缓存 ==========

    /**
     * 设置字段来源缓存
     */
    public void setFieldSources(String tableName, Long recordId, Object fieldSources) {
        String key = RedisKeyConstants.FIELD_SOURCES + tableName + ":" + recordId;
        redisTemplate.opsForValue().set(key, fieldSources, RedisKeyConstants.FIELD_SOURCES_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取字段来源缓存
     */
    public Object getFieldSources(String tableName, Long recordId) {
        String key = RedisKeyConstants.FIELD_SOURCES + tableName + ":" + recordId;
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除字段来源缓存
     */
    public void deleteFieldSources(String tableName, Long recordId) {
        String key = RedisKeyConstants.FIELD_SOURCES + tableName + ":" + recordId;
        redisTemplate.delete(key);
    }

    // ========== 验证码模块缓存 ==========

    /**
     * 设置邮箱验证码缓存
     */
    public void setEmailCode(String email, String code) {
        String key = RedisKeyConstants.EMAIL_CODE + email;
        redisTemplate.opsForValue().set(key, code, RedisKeyConstants.VERIFY_CODE_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取邮箱验证码缓存
     */
    public String getEmailCode(String email) {
        String key = RedisKeyConstants.EMAIL_CODE + email;
        Object value = redisTemplate.opsForValue().get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 删除邮箱验证码缓存
     */
    public void deleteEmailCode(String email) {
        String key = RedisKeyConstants.EMAIL_CODE + email;
        redisTemplate.delete(key);
    }

    /**
     * 设置发送频率限制缓存
     */
    public void setSendFrequency(String identifier) {
        String key = RedisKeyConstants.SEND_FREQUENCY + identifier;
        redisTemplate.opsForValue().set(key, "1", RedisKeyConstants.SEND_FREQUENCY_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 检查发送频率限制
     */
    public boolean checkSendFrequency(String identifier) {
        String key = RedisKeyConstants.SEND_FREQUENCY + identifier;
        return redisTemplate.hasKey(key);
    }

    // ========== 集成模块缓存 ==========

    /**
     * 设置 eBay OAuth 令牌缓存
     */
    public void setEbayOAuthToken(String token, long expireSeconds) {
        redisTemplate.opsForValue().set(RedisKeyConstants.EBAY_OAUTH_TOKEN, token, expireSeconds, TimeUnit.SECONDS);
    }

    /**
     * 获取 eBay OAuth 令牌缓存
     */
    public String getEbayOAuthToken() {
        Object value = redisTemplate.opsForValue().get(RedisKeyConstants.EBAY_OAUTH_TOKEN);
        return value != null ? value.toString() : null;
    }

    /**
     * 设置 eBay 同步状态缓存
     */
    public void setEbaySyncStatus(Object status) {
        redisTemplate.opsForValue().set(RedisKeyConstants.EBAY_SYNC_STATUS, status, RedisKeyConstants.EBAY_SYNC_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取 eBay 同步状态缓存
     */
    public Object getEbaySyncStatus() {
        return redisTemplate.opsForValue().get(RedisKeyConstants.EBAY_SYNC_STATUS);
    }

    /**
     * 设置 eBay 待处理模型缓存
     */
    public void setEbayPendingModels(Object models) {
        redisTemplate.opsForValue().set(RedisKeyConstants.EBAY_SYNC_PENDING_MODELS, models,
            RedisKeyConstants.EBAY_SYNC_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 获取 eBay 待处理模型缓存
     */
    public Object getEbayPendingModels() {
        return redisTemplate.opsForValue().get(RedisKeyConstants.EBAY_SYNC_PENDING_MODELS);
    }

    /**
     * 添加已同步商品到缓存
     */
    public void addEbaySyncedItem(String itemId) {
        redisTemplate.opsForSet().add(RedisKeyConstants.EBAY_SYNC_SYNCED_ITEMS, itemId);
        redisTemplate.expire(RedisKeyConstants.EBAY_SYNC_SYNCED_ITEMS,
            RedisKeyConstants.EBAY_SYNC_TIMEOUT, TimeUnit.SECONDS);
    }

    /**
     * 检查商品是否已同步
     */
    public boolean isEbayItemSynced(String itemId) {
        return redisTemplate.opsForSet().isMember(RedisKeyConstants.EBAY_SYNC_SYNCED_ITEMS, itemId);
    }

    /**
     * 获取所有已同步的商品ID
     */
    public Object getEbaySyncedItems() {
        return redisTemplate.opsForSet().members(RedisKeyConstants.EBAY_SYNC_SYNCED_ITEMS);
    }

    // ========== 通用缓存操作 ==========

    /**
     * 设置缓存（自定义过期时间）
     */
    public void setCache(String key, Object value, long timeout, TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 获取缓存
     */
    public Object getCache(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 删除缓存
     */
    public void deleteCache(String key) {
        redisTemplate.delete(key);
    }

    /**
     * 检查缓存是否存在
     */
    public boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 设置缓存过期时间
     */
    public void expire(String key, long timeout, TimeUnit timeUnit) {
        redisTemplate.expire(key, timeout, timeUnit);
    }

    /**
     * 获取缓存剩余过期时间（秒）
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    // ========== 基本对象操作 ==========

    /**
     * 缓存基本的对象，Integer、String、实体类等
     */
    public <T> void setCacheObject(final String key, final T value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 缓存基本的对象，Integer、String、实体类等
     */
    public <T> void setCacheObject(final String key, final T value, final Long timeout, final TimeUnit timeUnit) {
        redisTemplate.opsForValue().set(key, value, timeout, timeUnit);
    }

    /**
     * 获得缓存的基本对象
     */
    @SuppressWarnings("unchecked")
    public <T> T getCacheObject(final String key) {
        return (T) redisTemplate.opsForValue().get(key);
    }

    /**
     * 原子递增 Redis 中的值
     */
    public Long incrementCacheObject(final String key, final long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 删除单个对象
     */
    public boolean deleteObject(final String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 删除集合对象
     */
    public boolean deleteObject(final Collection<String> collection) {
        return redisTemplate.delete(collection) > 0;
    }

    /**
     * 批量获取缓存的基本对象
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> multiGetCacheObject(final Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyList();
        }
        return (List<T>) redisTemplate.opsForValue().multiGet(keys);
    }

    // ========== List操作 ==========

    /**
     * 缓存List数据
     */
    public <T> long setCacheList(final String key, final List<T> values) {
        Long count = redisTemplate.opsForList().rightPushAll(key, values);
        return count == null ? 0 : count;
    }

    /**
     * 获得缓存的list对象
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getCacheList(final String key) {
        return (List<T>) redisTemplate.opsForList().range(key, 0, -1);
    }

    // ========== Set操作 ==========

    /**
     * 缓存Set
     */
    @SuppressWarnings("unchecked")
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final Set<T> values) {
        BoundSetOperations<String, T> setOperation = (BoundSetOperations<String, T>) redisTemplate.boundSetOps(key);
        for (T value : values) {
            setOperation.add(value);
        }
        return setOperation;
    }

    /**
     * 缓存Set
     */
    @SuppressWarnings("unchecked")
    public <T> BoundSetOperations<String, T> setCacheSet(final String key, final T value) {
        BoundSetOperations<String, T> setOperation = (BoundSetOperations<String, T>) redisTemplate.boundSetOps(key);
        setOperation.add(value);
        return setOperation;
    }

    /**
     * 获得缓存的set
     */
    @SuppressWarnings("unchecked")
    public <T> Set<T> getCacheSet(final String key) {
        return (Set<T>) redisTemplate.opsForSet().members(key);
    }

    /**
     * 删除缓存的Set
     */
    public Long deleteCacheSet(final String key, final Object... values) {
        return redisTemplate.opsForSet().remove(key, values);
    }

    // ========== ZSet操作 ==========

    /**
     * 缓存ZSet
     */
    @SuppressWarnings("unchecked")
    public <T> BoundZSetOperations<String, T> setCacheZSet(final String key, final Object value, final Double score) {
        BoundZSetOperations<String, T> zSetOperation = (BoundZSetOperations<String, T>) redisTemplate.boundZSetOps(key);
        zSetOperation.add((T) value, score);
        return zSetOperation;
    }

    /**
     * 获得缓存的ZSet
     */
    @SuppressWarnings("unchecked")
    public <T> Set<T> getCacheZSet(final String key) {
        BoundZSetOperations<String, T> zSetOperation = (BoundZSetOperations<String, T>) redisTemplate.boundZSetOps(key);
        return zSetOperation.range(0, -1);
    }

    /**
     * 删除缓存的ZSet
     */
    public Long deleteCacheZSet(final String key, final Object... values) {
        return redisTemplate.opsForZSet().remove(key, values);
    }

    /**
     * 获取ZSet中元素的倒序排名
     */
    public Long getCacheZSetReverseRank(final String key, final Object value) {
        try {
            Long reverseRank = redisTemplate.opsForZSet().reverseRank(key, value);
            return reverseRank == null ? null : reverseRank + 1;
        } catch (Exception e) {
            log.error("从zset {} 中获取元素值 {} 的倒序排名失败", key, value);
            return 0L;
        }
    }

    /**
     * 获取ZSet中元素的数量
     */
    public Long getCacheZSetSize(final String key) {
        try {
            BoundZSetOperations<String, Object> zSetOperation = redisTemplate.boundZSetOps(key);
            return zSetOperation.size();
        } catch (Exception e) {
            log.error("获取zset {} 的元素数量失败", key);
            return 0L;
        }
    }

    // ========== Hash操作 ==========

    /**
     * 缓存Map
     */
    public <T> void setCacheMap(final String key, final Map<String, T> dataMap) {
        if (dataMap != null) {
            redisTemplate.opsForHash().putAll(key, dataMap);
        }
    }

    /**
     * 获得缓存的Map
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public <T> Map<String, T> getCacheMap(final String key) {
        return (Map) redisTemplate.opsForHash().entries(key);
    }

    /**
     * 往Hash中存入数据
     */
    public <T> void setCacheMapValue(final String key, final String hKey, final T value) {
        redisTemplate.opsForHash().put(key, hKey, value);
    }

    /**
     * 获取Hash中的数据
     */
    @SuppressWarnings("unchecked")
    public <T> T getCacheMapValue(final String key, final String hKey) {
        return (T) redisTemplate.opsForHash().get(key, hKey);
    }

    /**
     * 获取多个Hash中的数据
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> getMultiCacheMapValue(final String key, final Collection<Object> hKeys) {
        return (List<T>) redisTemplate.opsForHash().multiGet(key, hKeys);
    }

    /**
     * 删除Hash中的某条数据
     */
    public boolean deleteCacheMapValue(final String key, final String hKey) {
        return redisTemplate.opsForHash().delete(key, hKey) > 0;
    }

    // ========== 高级操作 ==========

    /**
     * 获得缓存的基本对象列表
     */
    public Collection<String> keys(final String pattern) {
        return redisTemplate.keys(pattern);
    }

    /**
     * 执行lua脚本
     */
    public <T> T execute(RedisScript<T> script, List<String> keys, Object... values) {
        return redisTemplate.execute(script, keys, values);
    }
}
