package ai.pricefox.mallfox;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableRetry
@EnableScheduling
@EnableAsync
@SpringBootApplication
@MapperScan("ai.pricefox.mallfox.mapper.*")
public class MallfoxApplication {

	public static void main(String[] args) {
		SpringApplication.run(MallfoxApplication.class, args);
	}

}
