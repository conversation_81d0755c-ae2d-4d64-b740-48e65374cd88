package ai.pricefox.mallfox.vo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品详情响应 VO
 */
@Data
@Schema(description = "商品详情响应")
public class ProductDetailRespVO {

    @Schema(description = "基础商品信息")
    private BaseProductVO baseProduct;

    @Schema(description = "商品变体列表")
    private List<ProductVariantVO> variants;

    /**
     * 基础商品信息 VO - 仅包含StandardProduct表数据
     */
    @Data
    @Schema(description = "基础商品信息")
    public static class BaseProductVO {
        @Schema(description = "SPU编码", example = "SPU001")
        private String spuCode;

        @Schema(description = "商品标题", example = "iPhone 15 Pro")
        private String title;

        @Schema(description = "短标题", example = "iPhone 15 Pro")
        private String sortTitle;

        @Schema(description = "品牌名称", example = "Apple")
        private String brandName;

        @Schema(description = "品牌编码", example = "APPLE")
        private String brandCode;

        @Schema(description = "分类编码", example = "PHONE")
        private String categoryCode;

        @Schema(description = "商品简介", example = "全新iPhone 15 Pro")
        private String brief;

        @Schema(description = "商品详细描述")
        private String description;

        @Schema(description = "商品主图URL")
        private String masterImgUrl;

        @Schema(description = "是否单一变体", example = "false")
        private Boolean hasOnlyDefaultVariant;

        @Schema(description = "是否已发布", example = "true")
        private Boolean published;

        @Schema(description = "发布时间")
        private java.time.LocalDateTime publishedAt;

        @Schema(description = "商品标签", example = "手机,苹果,Pro")
        private String tags;

        @Schema(description = "SEO标题")
        private String seoTitle;

        @Schema(description = "SEO描述")
        private String seoDescription;

        @Schema(description = "SEO关键词")
        private String seoKeywords;
    }

    /**
     * 商品变体 VO - 包含完整变体信息
     */
    @Data
    @Schema(description = "商品变体")
    public static class ProductVariantVO {
        @Schema(description = "SKU编码", example = "SKU002")
        private String skuCode;

        @Schema(description = "变体标题", example = "iPhone 15 Pro 256GB 深空黑色")
        private String title;

        @Schema(description = "变体属性JSON", example = "{\"color\":\"深空黑色\",\"storage\":\"256GB\",\"condition\":\"全新\"}")
        private String optionJson;

        @Schema(description = "排序", example = "1")
        private Integer sort;

        // 价格信息
        @Schema(description = "当前价格 USD", example = "999.99")
        private BigDecimal price;

        @Schema(description = "采购价格 USD", example = "1199.99")
        private BigDecimal procurePrice;

        @Schema(description = "成本价格 USD", example = "800.00")
        private BigDecimal costPrice;

        @Schema(description = "折扣百分比", example = "16.67")
        private BigDecimal discountPercentage;

        // 库存信息
        @Schema(description = "库存数量", example = "100")
        private Integer inventoryQuantity;

        // 规格信息
        @Schema(description = "重量", example = "187.5")
        private BigDecimal weight;

        @Schema(description = "重量单位", example = "1.0")
        private BigDecimal weightUnit;

        @Schema(description = "高度", example = "146.6")
        private BigDecimal height;

        @Schema(description = "长度", example = "70.6")
        private BigDecimal length;

        @Schema(description = "宽度", example = "8.25")
        private BigDecimal width;

        // 其他信息
        @Schema(description = "SKU", example = "IPHONE15PRO256GB")
        private String sku;

        @Schema(description = "条形码", example = "123456789")
        private String barcode;

        @Schema(description = "备注", example = "备注信息")
        private String note;

        @Schema(description = "提示信息", example = "特殊提示")
        private String remarks;

        // 销售相关信息
        @Schema(description = "近30天销量", example = "150")
        private Integer salesLast30Days;

        @Schema(description = "分期信息", example = "支持24期免息")
        private String installmentInfo;

        // 评价相关信息
        @Schema(description = "总评论数", example = "1250")
        private Integer totalReviews;

        @Schema(description = "评论评分", example = "4.5")
        private BigDecimal reviewRating;

        // 价格相关信息
        @Schema(description = "商品链接", example = "https://example.com/product/123")
        private String itemUrl;

        @Schema(description = "全渠道最低价", example = "899.99")
        private BigDecimal lowestPrice;

        @Schema(description = "全渠道最高价", example = "1299.99")
        private BigDecimal highestPrice;

        // 个性化信息（每个变体独立）
        @Schema(description = "是否收藏", example = "false")
        private Boolean isFavorited;

        @Schema(description = "30天价格历史数据")
        private List<PriceHistoryPointVO> priceHistory;
    }

    /**
     * 价格历史点 VO
     */
    @Data
    @Schema(description = "价格历史点")
    public static class PriceHistoryPointVO {
        @Schema(description = "日期", example = "2024-01-15")
        private String date;

        @Schema(description = "最低价", example = "999.99")
        private BigDecimal lowestPrice;

        @Schema(description = "平均价", example = "1099.99")
        private BigDecimal averagePrice;
    }
}