package ai.pricefox.mallfox.vo.category;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 分类信息创建请求VO
 */
@Data
@Schema(description = "分类信息创建请求")
public class CategoryInfoCreateReqVO {

    @Schema(description = "父分类ID", example = "0")
    @NotNull(message = "父分类ID不能为空")
    private Long parentId;

    @Schema(description = "分类名称", example = "智能手机")
    @NotBlank(message = "分类名称不能为空")
    private String name;

    @Schema(description = "分类层级", example = "1")
    @NotNull(message = "分类层级不能为空")
    private Integer level;

    @Schema(description = "分类图标", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;
}
