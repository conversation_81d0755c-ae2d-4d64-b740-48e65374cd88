package ai.pricefox.mallfox.vo.category;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 分类信息响应VO
 */
@Data
@Schema(description = "分类信息响应")
public class CategoryInfoRespVO {

    @Schema(description = "分类ID", example = "1")
    private Long id;

    @Schema(description = "父分类ID", example = "0")
    private Long parentId;

    @Schema(description = "分类名称", example = "智能手机")
    private String name;

    @Schema(description = "分类层级", example = "1")
    private Integer level;

    @Schema(description = "分类图标", example = "https://example.com/icon.png")
    private String iconUrl;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;

    @Schema(description = "创建时间", example = "2023-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2023-01-01 12:00:00")
    private LocalDateTime updateTime;
}
