package ai.pricefox.mallfox.vo.category;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分类信息分页查询请求VO
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "分类信息分页查询请求")
public class CategoryInfoPageReqVO extends PageParam {

    @Schema(description = "父分类ID", example = "0")
    private Long parentId;

    @Schema(description = "分类名称", example = "智能手机")
    private String name;

    @Schema(description = "分类层级", example = "1")
    private Integer level;

    @Schema(description = "是否激活", example = "true")
    private Boolean isActive;
}
