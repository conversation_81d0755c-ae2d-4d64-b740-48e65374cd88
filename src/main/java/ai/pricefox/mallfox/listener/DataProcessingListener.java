package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.domain.standard.FinalProduct;
import ai.pricefox.mallfox.event.DataMergedEvent;
import ai.pricefox.mallfox.event.DataStandardizedEvent;
import ai.pricefox.mallfox.event.FinalProductReadyEvent;
import ai.pricefox.mallfox.event.RawDataCollectedEvent;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.standard.FinalProductService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据处理监听器
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DataProcessingListener {
    private final FinalProductService finalProductService;
    private final ObjectMapper objectMapper;

    // 跟踪已完成标准化的平台
    private final Map<String, Map<String, Object>> standardizedPlatforms = new ConcurrentHashMap<>();

    /**
     * 监听原始数据采集完成事件
     */
    @EventListener
    public void handleRawDataCollected(RawDataCollectedEvent event) {
        log.info("接收到原始数据采集事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        // 触发数据合并
    }

    /**
     * 监听数据合并完成事件
     */
    @EventListener
    public void handleDataMerged(DataMergedEvent event) {
        log.info("接收到数据合并事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());

        try {
            // 创建ProductDataDTO对象用于标准化
            ProductDataDTO productData = objectMapper.convertValue(event.getMergedData(), ProductDataDTO.class);
            productData.setSourcePlatform(ai.pricefox.mallfox.enums.ProductPlatformEnum.valueOf(event.getPlatformCode()));

            // 触发数据标准化
        } catch (Exception e) {
            log.error("处理合并数据时出错", e);
        }
    }

    /**
     * 监听数据标准化完成事件
     */
    @Async
    @EventListener
    public void handleDataStandardized(DataStandardizedEvent event) {
        log.info("接收到数据标准化事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());

        // 记录已完成标准化的平台
        String key = event.getSkuId() + "_" + event.getPlatformCode();
        Map<String, Object> platformData = standardizedPlatforms.computeIfAbsent(key, k -> new HashMap<>());
        platformData.put("data", event.getAfterData());
        platformData.put("timestamp", System.currentTimeMillis());

        // 检查是否所有平台都已完成标准化 (这里假设需要3个平台)
        long completedPlatforms = standardizedPlatforms.keySet().stream().mapToLong(k -> k.startsWith(event.getSkuId() + "_") ? 1 : 0).sum();

        if (completedPlatforms >= 3) {
            // 触发最终商品生成
            createFinalProduct(event.getSkuId());
        }
    }

    /**
     * 创建最终商品
     *
     * @param skuId SKU ID
     */
    private void createFinalProduct(String skuId) {
        log.info("开始创建最终商品: skuId={}", skuId);

        try {
            // 创建最终商品对象
            FinalProduct finalProduct = new FinalProduct();
            finalProduct.setSkuId(skuId);
            finalProduct.setStatus("DRAFT"); // 初始状态为草稿

            // 合并所有平台的标准化数据
            Map<String, Object> finalData = new HashMap<>();
            standardizedPlatforms.entrySet().stream().filter(entry -> entry.getKey().startsWith(skuId + "_")).forEach(entry -> finalData.putAll((Map<String, Object>) entry.getValue().get("data")));

            // 将合并后的数据保存为JSON字符串
            finalProduct.setStandardData(objectMapper.writeValueAsString(finalData));
            finalProduct.setCreateTime(LocalDateTime.now());
            finalProduct.setUpdateTime(LocalDateTime.now());

            // 保存到数据库
            finalProductService.save(finalProduct);

            log.info("最终商品创建完成: skuId={}", skuId);
        } catch (Exception e) {
            log.error("创建最终商品时出错: skuId={}", skuId, e);
        }
    }

    /**
     * 监听最终商品准备完成事件
     */
    @EventListener
    public void handleFinalProductReady(FinalProductReadyEvent event) {
        log.info("接收到最终商品准备完成事件: skuId={}", event.getSkuId());
        // 处理最终商品数据
    }
}
