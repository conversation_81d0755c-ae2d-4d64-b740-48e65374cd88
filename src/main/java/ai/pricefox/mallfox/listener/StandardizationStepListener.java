package ai.pricefox.mallfox.listener;

import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.domain.standard.mongo.RawDataService;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.event.StandardizationCompletedEvent;
import ai.pricefox.mallfox.event.StandardizationStepEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.service.rules.DataStandardizationService;
import ai.pricefox.mallfox.service.rules.StandardDataCacheService;
import cn.hutool.http.useragent.Platform;
import com.google.gson.Gson;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 标准化步骤监听器
 * 负责监听和处理标准化流程中的各个步骤事件
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StandardizationStepListener {

    private final DataStandardizationService dataStandardizationService;
    private final StandardDataCacheService standardDataCacheService;
    private final RawDataService rawDataService;

    /**
     * 处理数据合并事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleMargeDataStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.MARGE_DATA) {
            return;
        }

        log.info("[标准化步骤] 开始处理数据合并步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 数据合并步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行字段映射规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.FIELD_MAPPING, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());
        Gson gson = new Gson();
        // 保存源数据到mongo
        ProductPlatformEnum productPlatformEnum = ProductPlatformEnum.valueOf(event.getOriginalDataMap().get("sourcePlatform").toString());
        DataChannelEnum dataChannelEnum = DataChannelEnum.valueOf(event.getOriginalDataMap().get("dataChannel").toString());

        processedData.setPlatformName(productPlatformEnum);
        processedData.setSourceType(dataChannelEnum);

        List<PlatformFieldMapping> platformFieldMappingList = standardDataCacheService.getPlatformFieldMapping();
        PlatformFieldMapping platformFieldMapping = platformFieldMappingList.stream().filter(s -> s.getPlatformCode().equals(event.getPlatformCode()) && s.getStandardFieldCode().equals("Raw0000003")).findFirst().orElse(null);

        if (platformFieldMapping == null) {
            log.error("[标准化步骤] 未找到原始SKU字段配置，无法生成产品标识");
            return;
        }
        processedData.setSkuId(platformFieldMapping.getSourceFieldName());

        String productIdentifier = dataStandardizationService.generateProductIdentifierFromMap(platformFieldMapping.getSourceFieldName(), processedData.getSourceType(), processedData.getPlatformName());
        String dataId = rawDataService.saveRawData(event.getOriginalDataMap().get("platformCode").toString(), productIdentifier, gson.toJson(event.getOriginalDataMap()));
        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.CATEGORY_STANDARDIZATION, "数据合并", "品类标准化");
    }

    /**
     * 处理字段映射步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleFieldMappingStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.FIELD_MAPPING) {
            return;
        }

        log.info("[标准化步骤] 开始处理字段映射步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 字段映射步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行字段映射后处理规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.FIELD_MAPPING, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.CATEGORY_STANDARDIZATION, "字段映射", "品类标准化");
    }

    /**
     * 处理品类标准化步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleCategoryStandardizationStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.CATEGORY_STANDARDIZATION) {
            return;
        }

        log.info("[标准化步骤] 开始处理品类标准化步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 品类标准化步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行品类标准化规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.CATEGORY_STANDARDIZATION, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.BRAND_STANDARDIZATION, "品类标准化", "品牌标准化");
    }

    /**
     * 处理品牌标准化步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleBrandStandardizationStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.BRAND_STANDARDIZATION) {
            return;
        }

        log.info("[标准化步骤] 开始处理品牌标准化步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 品牌标准化步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行品牌标准化规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.BRAND_STANDARDIZATION, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.MODEL_STANDARDIZATION, "品牌标准化", "型号标准化");
    }

    /**
     * 处理型号标准化步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleModelStandardizationStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.MODEL_STANDARDIZATION) {
            return;
        }

        log.info("[标准化步骤] 开始处理型号标准化步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 型号标准化步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行型号标准化规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.MODEL_STANDARDIZATION, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.COLOR_STANDARDIZATION, "型号标准化", "颜色标准化");
    }

    /**
     * 处理颜色标准化步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleColorStandardizationStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.COLOR_STANDARDIZATION) {
            return;
        }

        log.info("[标准化步骤] 开始处理颜色标准化步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 品色标准化步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行颜色标准化规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.COLOR_STANDARDIZATION, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.STORAGE_STANDARDIZATION, "颜色标准化", "存储标准化");
    }

    /**
     * 处理存储标准化步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleStorageStandardizationStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.STORAGE_STANDARDIZATION) {
            return;
        }

        log.info("[标准化步骤] 开始处理存储标准化步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 存储标准化步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行存储标准化规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.STORAGE_STANDARDIZATION, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.INDISTINGUISHABLE_PROCESSING, "存储标准化", "无差异处理");
    }

    /**
     * 处理无差异处理步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleNoDifferenceProcessingStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.INDISTINGUISHABLE_PROCESSING) {
            return;
        }

        log.info("[标准化步骤] 开始处理无差异处理步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 无差异处理步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行无差异处理规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.INDISTINGUISHABLE_PROCESSING, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.INVALID_VALUE_PROCESSING, "无差异处理", "无效值处理");
    }

    /**
     * 处理无效值处理步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleInvalidValueProcessingStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.INVALID_VALUE_PROCESSING) {
            return;
        }

        log.info("[标准化步骤] 开始处理无效值处理步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 无效值处理步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行无效值处理规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.INVALID_VALUE_PROCESSING, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        dataStandardizationService.processAndForwardEvent(event, StandardizationSubStepEnum.FINAL_STANDARDIZATION, "无效值处理", "最终标准化");
    }

    /**
     * 处理最终标准化步骤完成事件
     *
     * @param event 标准化步骤事件
     */
    @EventListener
    public void handleFinalStandardizationStep(StandardizationStepEvent event) {
        if (event.getStep() != StandardizationSubStepEnum.FINAL_STANDARDIZATION) {
            return;
        }

        log.info("[标准化步骤] 开始处理最终标准化步骤完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());
        log.debug("[标准化步骤] 最终标准化步骤前的数据: {}", event.getCurrentData().getFields());

        // 执行最终标准化规则组
        DynamicStandardProduct processedData = dataStandardizationService.executeStandardizationStepWithJson(StandardizationSubStepEnum.FINAL_STANDARDIZATION, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        log.debug("[标准化步骤] 最终标准化步骤后的数据: {}", processedData.getFields());
        log.info("[标准化步骤] 最终标准化步骤处理完成，准备发布标准化完成事件: skuId={}, platformCode={}", event.getSkuId(), event.getPlatformCode());

        // 发布标准化完成事件
        StandardizationCompletedEvent completedEvent = new StandardizationCompletedEvent(this, event.getSkuId(), event.getPlatformCode(), event.getOriginalDataMap(), processedData, event.getDataId());

        dataStandardizationService.publishEvent(completedEvent);
    }
}