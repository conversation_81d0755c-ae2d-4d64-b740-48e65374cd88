package ai.pricefox.mallfox.event;

import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 标准化完成事件
 * 表示整个标准化流程已完成
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class StandardizationCompletedEvent extends ApplicationEvent {
    private final String skuId;
    private final String platformCode;
    private final Map<String, Object> originalDataMap; // 用于JSON格式的原始数据
    private final DynamicStandardProduct standardizedData;
    private final String dataId;

    public StandardizationCompletedEvent(Object source, String skuId, String platformCode, Map<String, Object> originalDataMap, DynamicStandardProduct standardizedData, String dataId) {
        super(source);
        this.skuId = skuId;
        this.platformCode = platformCode;
        this.originalDataMap = originalDataMap;
        this.standardizedData = standardizedData;
        this.dataId = dataId;
    }
}