package ai.pricefox.mallfox.event;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * 标准化事件
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class DataStandardizedEvent extends ApplicationEvent {
    private final String skuId;
    private final String platformCode;
    private final String stepType;  // CATEGORY/BRAND/SPEC
    private final String beforeData;
    private final String afterData;
    private final String changeDetails;  // 字段变更详情

    public DataStandardizedEvent(Object source, String skuId, String platformCode, String stepType, String beforeData, String afterData, String changeDetails) {
        super(source);
        this.skuId = skuId;
        this.platformCode = platformCode;
        this.stepType = stepType;
        this.beforeData = beforeData;
        this.afterData = afterData;
        this.changeDetails = changeDetails;
    }
}