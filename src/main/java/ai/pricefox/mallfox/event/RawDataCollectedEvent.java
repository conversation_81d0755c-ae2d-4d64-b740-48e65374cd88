package ai.pricefox.mallfox.event;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 数据采集事件
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class RawDataCollectedEvent extends ApplicationEvent {
    private final String skuId;
    private final String platformCode;
    private final DataChannelEnum channelType;  // API/CRAWLER
    private final Map<String, Object> rawData;

    public RawDataCollectedEvent(Object source, String skuId, String platformCode, DataChannelEnum channelType, Map<String, Object> rawData) {
        super(source);
        this.skuId = skuId;
        this.platformCode = platformCode;
        this.channelType = channelType;
        this.rawData = rawData;
    }
}