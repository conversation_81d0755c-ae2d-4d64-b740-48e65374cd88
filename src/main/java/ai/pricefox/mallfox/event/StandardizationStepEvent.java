package ai.pricefox.mallfox.event;

import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 标准化步骤事件
 * 用于在标准化流程的每个步骤之间传递数据
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class StandardizationStepEvent extends ApplicationEvent {
    private final String skuId;
    private final String platformCode;
    private final StandardizationSubStepEnum step;
    private final DynamicStandardProduct currentData;
    private final String dataId; // 用于关联原始数据存储ID
    private final Map<String, Object> originalDataMap; // 用于JSON格式的原始数据

    public StandardizationStepEvent(Object source, String skuId, String platformCode, StandardizationSubStepEnum step, Map<String, Object> originalDataMap, DynamicStandardProduct currentData, String dataId) {
        super(source);
        this.skuId = skuId;
        this.platformCode = platformCode;
        this.step = step;
        this.currentData = currentData;
        this.dataId = dataId;
        this.originalDataMap = originalDataMap;
    }
}