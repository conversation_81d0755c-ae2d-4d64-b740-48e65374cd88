package ai.pricefox.mallfox.event;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Map;

/**
 * 合并数据事件
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class DataMergedEvent extends ApplicationEvent {
    private final String skuId;
    private final String platformCode;
    private final Map<String, Object> mergedData;

    public DataMergedEvent(Object source, String skuId, String platformCode, Map<String, Object> mergedData) {
        super(source);
        this.skuId = skuId;
        this.platformCode = platformCode;
        this.mergedData = mergedData;
    }
}