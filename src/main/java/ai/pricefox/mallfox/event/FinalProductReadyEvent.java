package ai.pricefox.mallfox.event;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Map;

/**
 * 最终商品事件
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class FinalProductReadyEvent extends ApplicationEvent {
    private final String skuId;
    private final Map<String, Object> finalData;
    private final List<String> platformCodes;  // 需要的平台数据

    public FinalProductReadyEvent(Object source, String skuId, Map<String, Object> finalData, List<String> platformCodes) {
        super(source);
        this.skuId = skuId;
        this.finalData = finalData;
        this.platformCodes = platformCodes;
    }
}