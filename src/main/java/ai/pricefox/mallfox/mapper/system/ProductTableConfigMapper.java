package ai.pricefox.mallfox.mapper.system;

import ai.pricefox.mallfox.domain.product.ProductTableConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品表格配置Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductTableConfigMapper extends BaseMapper<ProductTableConfig> {

    /**
     * 根据类型查询配置列表
     *
     * @param type 类型
     * @return 配置列表
     */
    List<ProductTableConfig> selectByType(@Param("type") Integer type);

    /**
     * 根据字段更新配置
     *
     * @param field 字段
     * @param type  类型
     * @param info  JSON信息
     * @return 更新数量
     */
    int updateByField(@Param("field") String field,
                      @Param("type") Integer type,
                      @Param("info") String info,
                      @Param("weight") Integer weight);

    /**
     * 根据字段查询配置
     *
     * @param field 字段
     * @return 配置信息
     */
    ProductTableConfig selectByField(@Param("field") String field);

    /**
     * 批量插入配置
     *
     * @param configs 配置列表
     * @return 插入数量
     */
    int batchInsert(@Param("configs") List<ProductTableConfig> configs);

    /**
     * 根据字段和类型查询配置
     *
     * @param field 字段
     * @param type  类型
     * @return 配置信息
     */
    ProductTableConfig selectByFieldAndType(@Param("field") String field, @Param("type") Integer type);

    /**
     * 根据类型查询配置列表（支持权重排序）
     */
    List<ProductTableConfig> selectByTypeWithSort(@Param("type") Integer type, @Param("tag") String tag);


    /**
     * 查询指定 tag 的最大 weight 值
     *
     * @param tag 表格标签
     * @return 最大 weight 值
     */
    String selectMaxWeightByTag(@Param("tag") String tag);

    /**
     * 查询指定 tag 的最小 weight 值
     *
     * @param tag 表格标签
     * @return 最小 weight 值
     */
    String selectMinWeightByTag(@Param("tag") String tag);

    /**
     * 查询指定 tag 下，当前 weight 值的上一条记录的 weight
     *
     * @param tag           表格标签
     * @param currentWeight 当前 weight 值
     * @return 上一条记录的 weight 值，如果没有则返回 null
     */
    String selectPrevWeightByTag(@Param("tag") String tag, @Param("currentWeight") String currentWeight);

    /**
     * 根据 ID 查询配置（仅查询 weight 字段）
     *
     * @param id 主键 ID
     * @return weight 值
     */
    String selectById(@Param("id") Integer id);


    /**
     * 更新权重
     *
     * @param id 主键 ID
     * @return weight 值
     * @return
     */
    int updateWeightById(@Param("id") Integer id, @Param("weight") String weight);
}
