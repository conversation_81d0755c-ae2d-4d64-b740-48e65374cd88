package ai.pricefox.mallfox.mapper.product;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 渠道商品报价表 Mapper
 * <AUTHOR>
 * @description 针对表【channel_offers(渠道商品报价表 (高频))】的数据库操作Mapper
 */
@Mapper
public interface ChannelOffersMapper extends BaseMapper<ChannelOffers> {

    /**
     * 根据SKU ID列表查询最低价格的渠道报价
     * 每个SKU只返回价格最低的一条记录
     * 
     * @param skuIds SKU ID列表
     * @return 渠道报价列表
     */
    @Select("<script>" +
            "SELECT co.* FROM channel_offers co " +
            "INNER JOIN (" +
            "  SELECT sku_id, MIN(price) as min_price " +
            "  FROM channel_offers " +
            "  WHERE sku_id IN " +
            "  <foreach collection='skuIds' item='skuId' open='(' separator=',' close=')'>" +
            "    #{skuId}" +
            "  </foreach>" +
            "  GROUP BY sku_id" +
            ") min_co ON co.sku_id = min_co.sku_id AND co.price = min_co.min_price " +
            "ORDER BY co.price ASC " +
            "LIMIT 100" +
            "</script>")
    List<ChannelOffers> selectLowestPriceBySkuIds(@Param("skuIds") List<String> skuIds);

    /**
     * 根据单个SKU ID和平台查询渠道报价
     * 
     * @param skuId SKU ID
     * @param sourcePlatform 平台名称
     * @return 渠道报价
     */
    @Select("SELECT * FROM channel_offers WHERE sku_id = #{skuId} AND source_platform = #{sourcePlatform} LIMIT 1")
    ChannelOffers selectBySkuIdAndPlatform(@Param("skuId") String skuId, @Param("sourcePlatform") String sourcePlatform);
}
