package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.mapper.product.ProductInfoMapper;
import ai.pricefox.mallfox.model.param.HomepageProductRequest;
import ai.pricefox.mallfox.model.response.ProductCardDTO;
import ai.pricefox.mallfox.service.product.HomepageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 首页商品服务实现类
 */
@Service
@Slf4j
public class HomepageServiceImpl implements HomepageService {

    @Autowired
    private ProductInfoMapper productInfoMapper;

    private static final Map<String, String> ORDER_BY_MAP = new HashMap<>();

    static {
        ORDER_BY_MAP.put("best-seller", "ps.sales_last_30_days DESC");
        // 假设 m=100, C=4.2 使用贝叶斯平均算法来计算Top-Rated排序
        ORDER_BY_MAP.put("top-rated", "((ps.total_reviews / (ps.total_reviews + 100)) * ps.average_rating) + ((100 / (ps.total_reviews + 100)) * 4.2) DESC");
        ORDER_BY_MAP.put("new-releases", "pi.release_date DESC, ps.create_time DESC");
        ORDER_BY_MAP.put("trending", "ps.average_rating DESC, ps.create_time DESC");
    }

    private static final Set<String> PREDEFINED_BRANDS = Set.of("apple", "samsung", "xiaomi");

    @Override
    public List<ProductCardDTO> getProductListForHomepage(HomepageProductRequest request) {
        String type = request.getType().toLowerCase();
        String brandName = null;
        String orderByClause;

        if (PREDEFINED_BRANDS.contains(type)) {
            //  将首字母大写逻将首字母大写逻辑移到Mapper中或保持原样辑移到Mapper中或保持原样，这里简化
            brandName = type.substring(0, 1).toUpperCase() + type.substring(1);
            // 品牌筛选时，使用默认的热度排序
            orderByClause = "ps.total_reviews DESC, ps.average_rating DESC";
        } else {
            //  从Map中获取排序规则，如果找不到则使用默认值
            orderByClause = ORDER_BY_MAP.getOrDefault(type, "ps.average_rating DESC, ps.create_time DESC");
        }

        log.info("Fetching homepage products. Type: {}, Brand: {}, OrderBy: {}", request.getType(), brandName, orderByClause);

        List<ProductCardDTO> productList = productInfoMapper.findProductCardList(brandName, orderByClause, request.getLimit());

        return productList;
    }
}