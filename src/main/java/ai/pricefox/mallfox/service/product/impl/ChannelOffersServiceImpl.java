package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import ai.pricefox.mallfox.mapper.product.ChannelOffersMapper;
import ai.pricefox.mallfox.service.product.ChannelOffersService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 渠道商品报价表 Service实现
 * <AUTHOR>
 * @description 针对表【channel_offers(渠道商品报价表 (高频))】的数据库操作Service实现
 */
@Service
@Slf4j
public class ChannelOffersServiceImpl extends ServiceImpl<ChannelOffersMapper, ChannelOffers>
        implements ChannelOffersService {

    @Override
    public List<ChannelOffers> getLowestPriceBySkuIds(List<String> skuIds) {
        try {
            if (skuIds == null || skuIds.isEmpty()) {
                log.warn("SKU ID列表为空，返回空列表");
                return List.of();
            }
            
            log.debug("查询SKU最低价格报价，SKU数量: {}", skuIds.size());
            return baseMapper.selectLowestPriceBySkuIds(skuIds);
        } catch (Exception e) {
            log.error("查询SKU最低价格报价失败: skuIds={}, error={}", skuIds, e.getMessage(), e);
            throw new RuntimeException("查询渠道报价失败", e);
        }
    }

    @Override
    public ChannelOffers getBySkuIdAndPlatform(String skuId, String sourcePlatform) {
        try {
            if (skuId == null || sourcePlatform == null) {
                log.warn("SKU ID或平台参数为空: skuId={}, sourcePlatform={}", skuId, sourcePlatform);
                return null;
            }
            
            log.debug("查询指定SKU和平台的报价: skuId={}, sourcePlatform={}", skuId, sourcePlatform);
            return baseMapper.selectBySkuIdAndPlatform(skuId, sourcePlatform);
        } catch (Exception e) {
            log.error("查询指定SKU和平台的报价失败: skuId={}, sourcePlatform={}, error={}", 
                skuId, sourcePlatform, e.getMessage(), e);
            throw new RuntimeException("查询渠道报价失败", e);
        }
    }
}
