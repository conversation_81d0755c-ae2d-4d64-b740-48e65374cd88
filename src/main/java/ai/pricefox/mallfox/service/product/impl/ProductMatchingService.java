package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.common.util.IdGenerator;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataSimplify;
import ai.pricefox.mallfox.domain.product.ProductPriceHistory;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.mapper.product.ProductDataOffersMapper;
import ai.pricefox.mallfox.mapper.product.ProductDataSimplifyMapper;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataSimplifyService;
import ai.pricefox.mallfox.service.product.strategy.DataUpdateStrategy;
import ai.pricefox.mallfox.service.product.util.ProductMatchingUtil;
import ai.pricefox.mallfox.service.tracking.TrackableEntityFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品唯一性匹配
 * @since 2025/6/21
 */
@Service
@Slf4j
public class ProductMatchingService {

    @Autowired
    private ProductDataSimplifyService simplifyService;

    @Autowired
    private ProductDataSimplifyMapper simplifyMapper;

    @Autowired
    private ProductDataOffersMapper offersMapper;

    @Autowired
    private TrackableEntityFactory trackableFactory;

    @Autowired
    private ProductDataOffersService offersService;


    /**
     * 匹配商品数据生成SPU（增强版：忽略大小写和空格，智能数据更新）
     * 注意：调用此方法前应该已经完成型号预处理
     *
     * @param dto
     * @return
     */
    public ProductDataSimplify findOrCreateSimplifyRecord(ProductDataDTO dto) {
        log.info("为 商品[{}] (model: {}) 查找或创建黄金记录", dto.getTitle(), dto.getModel());

        // 确保有处理后的型号（应该在调用前已经处理）
        if (!StringUtils.hasText(dto.getProcessedModel())) {
            log.warn("商品[{}]未进行型号预处理，使用原始型号", dto.getTitle());
            dto.setProcessedModel(dto.getModel());
        }

        // 步骤1：UPC维度精确匹配（如果有UPC码）/SKU维度精确匹配（忽略大小写和空格）
        ProductDataSimplify existingSku = findExistingRecordByUpc(dto);
        existingSku = existingSku == null ?  findExistingSkuRecord(dto) : existingSku;
        if (existingSku != null) {
            log.info("SKU匹配成功，找到黄金记录，SKU ID: {}", existingSku.getSkuId());

            // 根据数据渠道优先级进行智能更新
            boolean simplifyNeedUpdate = DataUpdateStrategy.updateRecordByDataChannelWithTracking(existingSku, dto);
            if (simplifyNeedUpdate || dto.getForceUpdate() == 1) {
                simplifyService.updateById(existingSku);
                log.info("商品[{}]黄金记录已更新，SKU ID: {}", dto.getTitle(), existingSku.getSkuId());
            }

            // 处理 ProductDataOffers 表（带追踪）
            handleProductDataOffersWithTracking(dto, existingSku.getSkuId(), existingSku.getSpuId());

            return existingSku;
        }

        log.info("商品[{}]未匹配到现有SKU，查找SPU进行型号合并", dto.getTitle());

        // 步骤2：SPU维度匹配（忽略大小写和空格）
        String spuId = findOrCreateSpuId(dto);

        // 步骤3：创建新的SKU记录
        ProductDataSimplify newSimplify = createNewSkuRecord(dto, spuId);
        simplifyService.save(newSimplify);
        log.info("商品[{}] 新的黄金记录已创建并保存，SKU ID: {}, SPU ID: {}",
                dto.getTitle(), newSimplify.getSkuId(), newSimplify.getSpuId());

        // 步骤4：创建对应的 ProductDataOffers 记录（带追踪）
        handleProductDataOffersWithTracking(dto, newSimplify.getSkuId(), newSimplify.getSpuId());

        return newSimplify;
    }

    /**
     * 创建新的SKU记录（带字段追踪）
     */
    private ProductDataSimplify createNewSkuRecordWithTracking(ProductDataDTO dto, String spuId) {
        String skuId = IdGenerator.generateSku();

        // 使用DataUpdateStrategy的带追踪创建方法
        ProductDataSimplify newSimplify = DataUpdateStrategy.createNewSimplifyRecordWithTracking(dto, spuId, skuId);

        return newSimplify;
    }

    /**
     * 查找现有的SKU记录（忽略大小写和空格）
     */
    private ProductDataSimplify findExistingSkuRecord(ProductDataDTO dto) {
        String normalizedModel = ProductMatchingUtil.normalizeString(dto.getProcessedModel());
        String normalizedColor = ProductMatchingUtil.normalizeString(dto.getColor());
        String normalizedStorage = ProductMatchingUtil.normalizeString(dto.getStorage());
        String normalizedServiceProvider = ProductMatchingUtil.normalizeString(dto.getServiceProvider());
        String normalizedCondition = ProductMatchingUtil.normalizeString(dto.getCondition());

        List<ProductDataSimplify> matchingRecords = simplifyMapper.selectByNormalizedFields(
                normalizedModel, normalizedColor, normalizedStorage,
                normalizedServiceProvider, normalizedCondition);

        return matchingRecords.isEmpty() ? null : matchingRecords.get(0);
    }

    /**
     * 根据UPC码查找现有记录
     */
    private ProductDataSimplify findExistingRecordByUpc(ProductDataDTO dto) {
        if (!StringUtils.hasText(dto.getUpcCode())) {
            log.debug("商品[{}]没有UPC码，跳过UPC匹配", dto.getTitle());
            return null;
        }

        try {
            ProductDataOffers upcMatched = offersMapper.selectByUpcCode(dto.getUpcCode());
            if (upcMatched != null) {
                log.debug("商品[{}]通过UPC码[{}]找到匹配记录，SKU ID: {}", 
                    dto.getTitle(), dto.getUpcCode(), upcMatched.getSkuId());
            } else {
                log.debug("商品[{}]通过UPC码[{}]未找到匹配记录", dto.getTitle(), dto.getUpcCode());
            }

            return simplifyMapper.selectOneBySkuAndSpu(upcMatched.getSkuId(), upcMatched.getSpuId());
        } catch (Exception e) {
            log.error("通过UPC码查询失败，UPC: {}", dto.getUpcCode(), e);
            return null;
        }
    }

    /**
     * 查找或创建SPU ID（忽略大小写和空格）
     */
    private String findOrCreateSpuId(ProductDataDTO dto) {
        String normalizedModel = ProductMatchingUtil.normalizeString(dto.getProcessedModel());

        List<ProductDataSimplify> spuRecords = simplifyMapper.selectByNormalizedModel(normalizedModel);

        if (!spuRecords.isEmpty()) {
            String spuId = spuRecords.get(0).getSpuId();
            log.info("商品[{}] SPU匹配成功，使用现有SPU ID: {}", dto.getTitle(), spuId);
            return spuId;
        } else {
            String spuId = IdGenerator.generateSpu();
            log.info("商品[{}]未匹配到现有SPU，生成新SPU ID: {}", dto.getTitle(), spuId);
            return spuId;
        }
    }

    /**
     * 创建新的SKU记录
     */
    private ProductDataSimplify createNewSkuRecord(ProductDataDTO dto, String spuId) {
        ProductDataSimplify newSimplify = new ProductDataSimplify();
        BeanUtils.copyProperties(dto, newSimplify);
        newSimplify.setConditionNew(dto.getCondition());
        newSimplify.setSpuId(spuId);
        newSimplify.setSkuId(IdGenerator.generateSku());

        // 保存处理后的型号和原始型号
        newSimplify.setModel(dto.getProcessedModel());
        newSimplify.setModelBack(dto.getModel());

        // 设置数据渠道
        if (dto.getDataChannel() == null || dto.getDataChannel().getCode() == 0) {
            newSimplify.setDataChannel(DataChannelEnum.CRAWLER.getCode());
        } else {
            newSimplify.setDataChannel(dto.getDataChannel().getCode());
        }

        return newSimplify;
    }

    /**
     * 处理 ProductDataOffers 表的数据
     */
    private void handleProductDataOffers(ProductDataDTO dto, String skuId, String spuId) {
        // 查找是否已存在相同平台的 offers 记录
        LambdaQueryWrapper<ProductDataOffers> offersQuery = new LambdaQueryWrapper<>();
        offersQuery.eq(ProductDataOffers::getSkuId, skuId)
                .eq(ProductDataOffers::getSpuId, spuId)
                .eq(ProductDataOffers::getSourcePlatform, dto.getSourcePlatform())
                .eq(ProductDataOffers::getPlatformSkuId, dto.getPlatformSkuId())
                .last("LIMIT 1");

        ProductDataOffers existingOffer = offersService.getOne(offersQuery);

        if (existingOffer != null) {
            log.info("找到现有的ProductDataOffers记录，ID: {}", existingOffer.getId());

            // 根据数据渠道优先级进行智能更新
            boolean offersNeedUpdate = DataUpdateStrategy.updateOffersRecordByDataChannel(existingOffer, dto);
            if (offersNeedUpdate || dto.getForceUpdate() == 1) {
                offersService.updateById(existingOffer);
                log.info("ProductDataOffers记录已更新，ID: {}", existingOffer.getId());
            }
        } else {
            log.info("创建新的ProductDataOffers记录，SKU ID: {}, SPU ID: {}", skuId, spuId);

            // 创建新的 offers 记录
            ProductDataOffers newOffer = DataUpdateStrategy.createNewOffersRecord(dto, skuId, spuId);
            offersService.save(newOffer);
            log.info("新的ProductDataOffers记录已创建，ID: {}", newOffer.getId());
        }
    }

    /**
     * 处理 ProductDataOffers 表的数据（带字段追踪和价格历史）
     */
    private void handleProductDataOffersWithTracking(ProductDataDTO dto, String skuId, String spuId) {
        log.debug("处理ProductDataOffers数据（带字段追踪和价格历史）: skuId={}", skuId);
        
        // 查找是否已存在相同平台的 offers 记录
        LambdaQueryWrapper<ProductDataOffers> offersQuery = new LambdaQueryWrapper<>();
        offersQuery.eq(ProductDataOffers::getSkuId, skuId)
                .eq(ProductDataOffers::getSpuId, spuId)
                .eq(ProductDataOffers::getSourcePlatform, dto.getSourcePlatform())
                .eq(ProductDataOffers::getPlatformSkuId, dto.getPlatformSkuId())
                .last("LIMIT 1");

        ProductDataOffers existingOffer = offersService.getOne(offersQuery);

        if (existingOffer != null) {
            log.info("找到现有的ProductDataOffers记录，ID: {}", existingOffer.getId());

            // 使用带追踪的更新方法
            boolean offersNeedUpdate = DataUpdateStrategy.updateOffersRecordByDataChannelWithTracking(existingOffer, dto);
            if (offersNeedUpdate || dto.getForceUpdate() == 1) {
                // 使用新的带价格历史的更新方法
                String triggerSource = ProductPriceHistory.TriggerSource.BATCH_PROCESS.getCode();
                boolean success = offersService.saveOrUpdateWithPriceHistory(existingOffer, triggerSource);
                if (success) {
                    log.info("ProductDataOffers记录已更新（含价格历史），ID: {}", existingOffer.getId());
                } else {
                    log.error("ProductDataOffers记录更新失败，ID: {}", existingOffer.getId());
                }
            }
        } else {
            log.info("创建新的ProductDataOffers记录，SKU ID: {}, SPU ID: {}", skuId, spuId);

            // 使用带追踪的创建方法
            ProductDataOffers newOffer = DataUpdateStrategy.createNewOffersRecordWithTracking(dto, skuId, spuId);
            
            // 使用新的带价格历史的保存方法
            String triggerSource = ProductPriceHistory.TriggerSource.BATCH_PROCESS.getCode();
            boolean success = offersService.saveOrUpdateWithPriceHistory(newOffer, triggerSource);
            
            if (success) {
                // 记录新建记录的字段来源（在保存成功后）
                DataUpdateStrategy.recordNewOffersFieldSources(newOffer, dto.getDataChannel().getCode(), dto.getSourcePlatform().getCode());
                log.info("新的ProductDataOffers记录已创建（含字段追踪和价格历史），ID: {}", newOffer.getId());
            } else {
                log.error("新的ProductDataOffers记录创建失败，skuId: {}", skuId);
            }
        }
    }

    /**
     * 匹配商品数据生成SPU（带字段追踪版本）
     */
    public ProductDataSimplify findOrCreateSimplifyRecordWithTracking(ProductDataDTO dto) {
        log.info("为 商品[{}] (model: {}) 查找或创建黄金记录（带字段追踪）", dto.getTitle(), dto.getModel());

        // 确保有处理后的型号（应该在调用前已经处理）
        if (!StringUtils.hasText(dto.getProcessedModel())) {
            log.warn("商品[{}]未进行型号预处理，使用原始型号", dto.getTitle());
            dto.setProcessedModel(dto.getModel());
        }

        // 步骤1：SKU维度精确匹配（忽略大小写和空格）
        ProductDataSimplify existingSku = findExistingSkuRecord(dto);
        if (existingSku != null) {
            log.info("SKU匹配成功，找到黄金记录，SKU ID: {}", existingSku.getSkuId());

            // 使用带追踪的更新方法
            boolean simplifyNeedUpdate = DataUpdateStrategy.updateRecordByDataChannelWithTracking(existingSku, dto);
            if (simplifyNeedUpdate || dto.getForceUpdate() == 1) {
                simplifyService.updateById(existingSku);
                log.info("商品[{}]黄金记录已更新，SKU ID: {}", dto.getTitle(), existingSku.getSkuId());
            }

            // 处理 ProductDataOffers 表（带追踪）
            handleProductDataOffersWithTracking(dto, existingSku.getSkuId(), existingSku.getSpuId());

            return existingSku;
        }

        log.info("商品[{}]未匹配到现有SKU，查找SPU进行型号合并", dto.getTitle());

        // 步骤2：SPU维度匹配（忽略大小写和空格）
        existingSku = findExistingRecordByUpc(dto);
        String spuId = existingSku != null ? existingSku.getSpuId() : findOrCreateSpuId(dto);

        // 步骤3：创建新的SKU记录（带追踪）
        ProductDataSimplify newSimplify = createNewSkuRecordWithTracking(dto, spuId);
        simplifyService.save(newSimplify);

        // 记录新建记录的字段来源
        DataUpdateStrategy.recordNewSimplifyFieldSources(newSimplify, dto.getDataChannel().getCode(), dto.getSourcePlatform().getCode());

        log.info("商品[{}] 新的黄金记录已创建并保存，SKU ID: {}, SPU ID: {}",
                dto.getTitle(), newSimplify.getSkuId(), newSimplify.getSpuId());

        // 步骤4：创建对应的 ProductDataOffers 记录（带追踪）
        handleProductDataOffersWithTracking(dto, newSimplify.getSkuId(), newSimplify.getSpuId());

        return newSimplify;
    }
}