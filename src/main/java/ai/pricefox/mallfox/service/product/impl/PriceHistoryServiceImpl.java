package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.common.exception.ServiceException;
import ai.pricefox.mallfox.config.ThreadPoolConfig;
import ai.pricefox.mallfox.mapper.product.PriceHistoryMapper;
import ai.pricefox.mallfox.model.response.PriceHistoryResponseDTO;
import ai.pricefox.mallfox.service.product.PriceHistoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.time.LocalDate;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * @description 价格历史服务实现类
 * <AUTHOR>
 * @date 2025-7-9 16:08:25
 * @version 1.0
 */
@Slf4j
@Service
public class PriceHistoryServiceImpl implements PriceHistoryService {

    @Autowired
    private PriceHistoryMapper priceHistoryMapper;

    @Autowired
    private ThreadPoolConfig threadPoolConfig;

    // 查询超时时间（秒）
    private static final int QUERY_TIMEOUT_SECONDS = 30;

    @Override
    public PriceHistoryResponseDTO getPriceHistory(String skuCode, String period, List<String> platforms) {
        log.info("开始查询价格历史 - skuCode: {}, period: {}, platforms: {}", skuCode, period, platforms);

        // 性能监控
        StopWatch stopWatch = new StopWatch("getPriceHistory");
        stopWatch.start("总体查询");

        try {
            // 计算日期范围
            LocalDate endDate = LocalDate.now();
            LocalDate startDate = calculateStartDate(period);
            log.debug("查询日期范围: {} 到 {}", startDate, endDate);

            // 确定查询的平台，如果传入为空，则默认为查询全平台聚合数据
            List<String> targetPlatforms = (CollectionUtils.isEmpty(platforms))
                    ? Collections.singletonList("ALL")
                    : platforms;
            log.debug("目标平台: {}", targetPlatforms);

            // 使用自定义线程池并行执行三个查询
            stopWatch.stop();
            stopWatch.start("并行查询");

            Future<List<PriceHistoryResponseDTO.DataPoint>> dataPointsFuture =
                threadPoolConfig.submitWithCustomPool(() -> {
                    log.debug("开始查询数据点 - 线程: {}", Thread.currentThread().getName());
                    try {
                        List<PriceHistoryResponseDTO.DataPoint> result =
                            priceHistoryMapper.findDataPoints(skuCode, startDate, endDate, targetPlatforms);
                        log.debug("数据点查询完成，返回 {} 个数据点", result != null ? result.size() : 0);
                        return result;
                    } catch (Exception e) {
                        log.error("查询数据点失败", e);
                        throw new RuntimeException("查询数据点失败: " + e.getMessage(), e);
                    }
                });

            Future<PriceHistoryResponseDTO.PricePoint> lowestInPeriodFuture =
                threadPoolConfig.submitWithCustomPool(() -> {
                    log.debug("开始查询周期最低价 - 线程: {}", Thread.currentThread().getName());
                    try {
                        PriceHistoryResponseDTO.PricePoint result =
                            priceHistoryMapper.findLowestPriceInPeriod(skuCode, startDate, endDate, targetPlatforms);
                        log.debug("周期最低价查询完成: {}", result);
                        return result;
                    } catch (Exception e) {
                        log.error("查询周期最低价失败", e);
                        throw new RuntimeException("查询周期最低价失败: " + e.getMessage(), e);
                    }
                });

            Future<PriceHistoryResponseDTO.PricePoint> latestPriceFuture =
                threadPoolConfig.submitWithCustomPool(() -> {
                    log.debug("开始查询最新价格 - 线程: {}", Thread.currentThread().getName());
                    try {
                        PriceHistoryResponseDTO.PricePoint result =
                            priceHistoryMapper.findLatestPrice(skuCode, targetPlatforms);
                        log.debug("最新价格查询完成: {}", result);
                        return result;
                    } catch (Exception e) {
                        log.error("查询最新价格失败", e);
                        throw new RuntimeException("查询最新价格失败: " + e.getMessage(), e);
                    }
                });

            stopWatch.stop();
            stopWatch.start("等待结果");

            // 等待所有查询完成，设置超时时间
            List<PriceHistoryResponseDTO.DataPoint> dataPoints;
            PriceHistoryResponseDTO.PricePoint lowestInPeriod;
            PriceHistoryResponseDTO.PricePoint latestPrice;

            try {
                dataPoints = dataPointsFuture.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                lowestInPeriod = lowestInPeriodFuture.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
                latestPrice = latestPriceFuture.get(QUERY_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.error("查询超时，取消未完成的任务");
                dataPointsFuture.cancel(true);
                lowestInPeriodFuture.cancel(true);
                latestPriceFuture.cancel(true);
                throw new RuntimeException("查询价格历史超时，请稍后重试", e);
            } catch (Exception e) {
                log.error("查询价格历史时发生错误", e);
                throw new RuntimeException("查询价格历史时发生错误: " + e.getMessage(), e);
            }

            stopWatch.stop();
            stopWatch.start("组装响应");

            // 组装响应
            PriceHistoryResponseDTO response = new PriceHistoryResponseDTO();
            response.setDataPoints(dataPoints != null ? dataPoints : Collections.emptyList());
            response.setLowestPriceInPeriod(lowestInPeriod);
            response.setLatestPrice(latestPrice);

            stopWatch.stop();

            log.info("价格历史查询完成 - skuCode: {}, 数据点数量: {}, 总耗时: {}ms",
                    skuCode,
                    dataPoints != null ? dataPoints.size() : 0,
                    stopWatch.getTotalTimeMillis());

            // 记录详细的性能信息
            if (log.isDebugEnabled()) {
                log.debug("性能详情: {}", stopWatch.prettyPrint());
            }

            // 记录线程池状态
            logThreadPoolStatus();

            return response;

        } catch (IllegalArgumentException e) {
            log.warn("参数错误: {}", e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("业务异常: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("系统异常", e);
            throw new RuntimeException("系统异常，请联系管理员", e);
        } finally {
            if (stopWatch.isRunning()) {
                stopWatch.stop();
            }
        }
    }

    /**
     * 根据传入的周期字符串计算开始日期。
     */
    private LocalDate calculateStartDate(String period) {
        if (period == null) {
            return LocalDate.now().minusMonths(1); // 默认1个月
        }
        switch (period.toUpperCase()) {
            case "1M": return LocalDate.now().minusMonths(1);
            case "6M": return LocalDate.now().minusMonths(6);
            case "1Y": return LocalDate.now().minusYears(1);
            case "2Y": return LocalDate.now().minusYears(2);
            case "ALL": return LocalDate.of(2000, 1, 1); // 一个足够早的日期代表“全部”
            case "3M":
            default: return LocalDate.now().minusMonths(3);
        }
    }

    /**
     * 记录线程池状态信息
     */
    private void logThreadPoolStatus() {
        try {
            ThreadPoolConfig.ThreadPoolStatus status = threadPoolConfig.getThreadPoolStatus();
            if (status != null && log.isDebugEnabled()) {
                log.debug("当前线程池状态: {}", status);
            }
        } catch (Exception e) {
            log.warn("获取线程池状态失败", e);
        }
    }
}
