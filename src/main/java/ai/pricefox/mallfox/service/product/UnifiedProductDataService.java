package ai.pricefox.mallfox.service.product;


import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.ProductDataReviewDTO;
import ai.pricefox.mallfox.model.dto.ProductPriceInventoryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 远程商品数据处理接口
 * @since 2025/6/23
 */
public interface UnifiedProductDataService {

    /**
     * 批量处理商品数据
     *
     * @param dtoList
     */
    void batchProcessProductData(List<ProductDataDTO> dtoList);

    /**
     * 批量处理新评论数据
     *
     * @param reviewList
     */
    void batchProcessNewReviews(List<ProductDataReviewDTO> reviewList);

    /**
     * 批量处理商品价格和库存信息
     * 专门用于价格和库存变化追踪
     */
    void batchProcessPriceAndInventory(List<ProductPriceInventoryDTO> dtoList);
}
