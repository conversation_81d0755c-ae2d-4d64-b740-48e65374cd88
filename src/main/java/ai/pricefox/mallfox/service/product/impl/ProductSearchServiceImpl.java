package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.mapper.product.ProductSearchMapper;
import ai.pricefox.mallfox.model.param.ProductSearchRequest;
import ai.pricefox.mallfox.model.response.*;
import ai.pricefox.mallfox.service.product.ProductSearchService;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 商品搜索服务实现
 * @since 2025/7/8
 */
@Service
@Slf4j
public class ProductSearchServiceImpl implements ProductSearchService {

    @Autowired
    private ProductSearchMapper productSearchMapper;

    @Override
    public ProductSearchResponse searchProducts(ProductSearchRequest request) {
        long startTime = System.currentTimeMillis();
        log.info("开始商品搜索，关键词: {}, 排序: {}", request.getKeyword(), request.getSortBy());

        ProductSearchResponse response = new ProductSearchResponse();
        //  执行商品搜索查询
        Page<ProductSearchRequest> page = new Page<>(request.getPageNo(), request.getPageSize());
        IPage<ProductSearchItemDTO> searchResult = productSearchMapper.searchProducts(page, request);
        if (searchResult.getTotal() == 0) {
            log.info("商品搜索结果为空，搜索参数：{}", JSONArray.toJSONString(request));
            return null;
        }
        // 设置商品列表和分页信息
        response.setProducts(searchResult.getRecords());
        response.setTotal(searchResult.getTotal());
        response.setKeyword(request.getKeyword());
        response.setSortBy(request.getSortBy());

        // 构建可用筛选器
        AvailableFiltersDTO availableFilters = buildAvailableFilters(request);
        response.setAvailableFilters(availableFilters);

        // 构建搜索统计信息
        SearchStatsDTO searchStats = buildSearchStats(request, startTime);
        response.setSearchStats(searchStats);

        log.info("商品搜索完成，返回 {} 条结果，总计 {} 条，耗时 {} ms",
                searchResult.getRecords().size(), searchResult.getTotal(),
                System.currentTimeMillis() - startTime);

        return response;
    }

    /**
     * 构建可用的筛选器选项
     */
    private AvailableFiltersDTO buildAvailableFilters(ProductSearchRequest request) {
        AvailableFiltersDTO filters = new AvailableFiltersDTO();

        // 1. 品牌筛选器
        List<FilterOption> brands = productSearchMapper.getAvailableBrands(request);
        filters.setBrands(brands);

        // 2. 动态规格筛选器
        List<DynamicSpecFilter> specFilters = buildSpecFilters(request);
        filters.setSpecFilters(specFilters);

        // 3. 价格区间统计
        Map<String, Object> priceStats = productSearchMapper.getPriceStatistics(request);
        filters.setPriceRange(priceStats);

        // 4. 评分分布
//        List<FilterOption> ratingOptions = productSearchMapper.getRatingDistribution(request);
//        filters.setRatingOptions(ratingOptions);

        return filters;
    }

    /**
     * 构建简化的规格筛选器
     */
    private List<DynamicSpecFilter> buildSpecFilters(ProductSearchRequest request) {
        List<DynamicSpecFilter> specFilters = List.of(
                createSpecFilter(1L, "Storage Capacity", productSearchMapper.getAvailableStorageOptions(request)),
                createSpecFilter(2L, "Color", productSearchMapper.getAvailableColorOptions(request)),
                createSpecFilter(3L, "Screen Size", productSearchMapper.getAvailableScreenSizeOptions(request)),
                createSpecFilter(4L, "RAM", productSearchMapper.getAvailableRamOptions(request)),
                createSpecFilter(5L, "Battery Capacity", productSearchMapper.getAvailableBatteryOptions(request))
        );

        // 过滤掉没有选项的筛选器
        return specFilters.stream()
                .filter(filter -> filter.getOptions() != null && !filter.getOptions().isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 创建规格筛选器
     */
    private DynamicSpecFilter createSpecFilter(Long attributeId, String attributeName, List<FilterOption> options) {
        DynamicSpecFilter filter = new DynamicSpecFilter();
        filter.setAttributeId(attributeId);
        filter.setAttributeName(attributeName);
        filter.setOptions(options);
        return filter;
    }

    /**
     * 构建搜索统计信息
     */
    private SearchStatsDTO buildSearchStats(ProductSearchRequest request, long startTime) {
        SearchStatsDTO searchStats = new SearchStatsDTO();

        // 计算搜索耗时
        searchStats.setSearchTime(System.currentTimeMillis() - startTime);

        // 获取品牌数量
        List<FilterOption> brands = productSearchMapper.getAvailableBrands(request);
        searchStats.setBrandCount(brands.size());

        // 获取价格统计
        Map<String, Object> priceStats = productSearchMapper.getPriceStatistics(request);
        if (priceStats != null && !priceStats.isEmpty()) {
            SearchStatsDTO.PriceRangeDTO priceRange = new SearchStatsDTO.PriceRangeDTO();
            priceRange.setMinPrice(getDoubleValue(priceStats.get("minPrice")));
            priceRange.setMaxPrice(getDoubleValue(priceStats.get("maxPrice")));
            priceRange.setAvgPrice(getDoubleValue(priceStats.get("avgPrice")));
            searchStats.setPriceRange(priceRange);
        }

        return searchStats;
    }

    /**
     * 安全地从Object转换为Double
     */
    private Double getDoubleValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
