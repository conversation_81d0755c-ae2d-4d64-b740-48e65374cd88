package ai.pricefox.mallfox.service.product;

import ai.pricefox.mallfox.domain.product.ChannelOffers;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 渠道商品报价表 Service
 * <AUTHOR>
 * @description 针对表【channel_offers(渠道商品报价表 (高频))】的数据库操作Service
 */
public interface ChannelOffersService extends IService<ChannelOffers> {

    /**
     * 根据SKU ID列表查询最低价格的渠道报价
     * 每个SKU只返回价格最低的一条记录
     * 
     * @param skuIds SKU ID列表
     * @return 渠道报价列表
     */
    List<ChannelOffers> getLowestPriceBySkuIds(List<String> skuIds);

    /**
     * 根据单个SKU ID和平台查询渠道报价
     * 
     * @param skuId SKU ID
     * @param sourcePlatform 平台名称
     * @return 渠道报价
     */
    ChannelOffers getBySkuIdAndPlatform(String skuId, String sourcePlatform);
}
