package ai.pricefox.mallfox.service.product.impl;

import ai.pricefox.mallfox.common.util.IdGenerator;
import ai.pricefox.mallfox.domain.product.ProductDataOffers;
import ai.pricefox.mallfox.domain.product.ProductDataReviews;
import ai.pricefox.mallfox.enums.DataChangeTypeEnum;
import ai.pricefox.mallfox.enums.DataTriggerSource;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.ProductDataReviewDTO;
import ai.pricefox.mallfox.service.product.ProductDataOffersService;
import ai.pricefox.mallfox.service.product.ProductDataReviewsService;
import ai.pricefox.mallfox.service.product.UnifiedProductDataService;
import ai.pricefox.mallfox.service.product.engine.ProductDataProcessingEngine;
import ai.pricefox.mallfox.service.product.engine.config.ProcessingConfig;
import ai.pricefox.mallfox.service.product.engine.processor.ModelProcessor;
import ai.pricefox.mallfox.config.FieldTrackingConfig;
import ai.pricefox.mallfox.service.product.strategy.DataUpdateStrategy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import ai.pricefox.mallfox.model.dto.ProductPriceInventoryDTO;
import ai.pricefox.mallfox.service.product.listener.InventoryChangeListener;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 商品数据统一处理服务实现
 * @since 2025/6/23
 */
@Service
@Slf4j
public class UnifiedProductDataServiceImpl implements UnifiedProductDataService {

    @Autowired
    private ProductMatchingService matchingService;

    @Autowired
    private ProductDataOffersService offersService;

    @Autowired
    private ProductDataReviewsService reviewsService;

    @Autowired
    private ProductDataProcessingEngine processingEngine;

    @Autowired
    private ModelProcessor modelProcessor;

    @Autowired
    private FieldTrackingConfig fieldTrackingConfig;

    @Autowired
    private InventoryChangeListener inventoryChangeListener;


    /**
     * 批量处理商品数据
     * 优化调用链，避免重复逻辑，提高性能
     */
    @Override
    @Transactional
    public void batchProcessProductData(List<ProductDataDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        log.info("批量处理商品数据，数据量：{}", dtoList.size());

        LocalDateTime startTime = LocalDateTime.now();
        int successCount = 0;
        int failedCount = 0;

        try {
            // 步骤1：对新数据进行型号预处理
            preprocessModels(dtoList);

            // 步骤2：数据匹配、保存和智能更新
            // 这一步已经包含了：SKU/SPU匹配、型号合并、数据更新、ProductDataOffers处理
            for (ProductDataDTO dto : dtoList) {
                try {
                    // 根据配置选择是否使用字段追踪版本
                    if (fieldTrackingConfig.getEnabled()) {
                        log.debug("使用字段追踪版本处理商品数据: title={}, dataChannel={}",
                                dto.getTitle(), dto.getDataChannel());
                        matchingService.findOrCreateSimplifyRecordWithTracking(dto);
                    } else {
                        log.debug("使用普通版本处理商品数据: title={}", dto.getTitle());
                        matchingService.findOrCreateSimplifyRecord(dto);
                    }
                    successCount++;
                } catch (Exception e) {
                    log.error("处理商品数据失败，型号: {}, 错误: {}", dto.getModel(), e.getMessage());
                    failedCount++;
                }
            }

            LocalDateTime endTime = LocalDateTime.now();
            long duration = java.time.Duration.between(startTime, endTime).toMillis();

            log.info("批量处理商品数据完成，总数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    dtoList.size(), successCount, failedCount, duration);

        } catch (Exception e) {
            log.error("批量处理商品数据失败", e);
            throw new RuntimeException("批量处理商品数据失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public void batchProcessNewReviews(List<ProductDataReviewDTO> reviewDtos) {
        if (CollectionUtils.isEmpty(reviewDtos)) {
            return;
        }

        String sourcePlatform = reviewDtos.get(0).getSourcePlatform();

        // 提取所有涉及的 platformSkuId，用于一次性查询offers表
        Set<String> platformSkuIds = reviewDtos.stream()
                .map(ProductDataReviewDTO::getPlatformSkuId)
                .collect(Collectors.toSet());

        // 提取所有涉及的 platformReviewId，用于一次性查询reviews表
        // 构建一个 platform-specific 的唯一键
//        Set<String> platformReviewKeys = reviewDtos.stream()
//                .map(dto -> dto.getSourcePlatform() + "::" + dto.getPlatformReviewId())
//                .collect(Collectors.toSet());


        //  查找评论与我们内部SKU的关联关系
        // 一次性查询 offers 表，建立 platformSkuId -> 自建sku_id 的映射
        Map<String, ProductDataOffers> offerMap = offersService.list(
                new LambdaQueryWrapper<ProductDataOffers>()
                        .in(ProductDataOffers::getPlatformSkuId, platformSkuIds)
        ).stream().collect(Collectors.toMap(ProductDataOffers::getPlatformSkuId, offer -> offer));


        // 查找哪些评论已经是存在的
        List<ProductDataReviews> existingReviews = reviewsService.list(
                new LambdaQueryWrapper<ProductDataReviews>()
                        .in(ProductDataReviews::getPlatformReviewId, reviewDtos.stream().map(ProductDataReviewDTO::getPlatformReviewId).collect(Collectors.toSet()))
                        .eq(ProductDataReviews::getSourcePlatform, sourcePlatform)
        );
        Set<String> existingReviewDbKeys = existingReviews.stream()
                .map(review -> review.getSourcePlatform() + "::" + review.getPlatformReviewId())
                .collect(Collectors.toSet());


        //  遍历DTO，筛选出真正需要新增的评论
        List<ProductDataReviews> reviewsToInsert = new ArrayList<>();
        for (ProductDataReviewDTO dto : reviewDtos) {
            //这条评论是否已经存在于数据库
            String currentReviewKey = dto.getSourcePlatform() + "::" + dto.getPlatformReviewId();
            if (existingReviewDbKeys.contains(currentReviewKey)) {
                log.info("评论已存在，跳过。PlatformReviewId: {}", dto.getPlatformReviewId());
                continue;
            }

            //这条评论的父商品是否存在于我们的系统中
            ProductDataOffers relatedOffer = offerMap.get(dto.getPlatformSkuId());
            if (relatedOffer == null || relatedOffer.getSkuId() == null) {
                log.warn("孤儿评论！未找到与 PlatformSkuId [{}] 关联的商品，此评论被忽略。", dto.getPlatformSkuId());
                continue;
            }

            // 验证通过，创建新的Review实体
            ProductDataReviews newReview = new ProductDataReviews();
            BeanUtils.copyProperties(dto, newReview);

            // 关联我们自己的ID体系
            newReview.setReviewId(IdGenerator.generateReviewId());
            newReview.setSkuId(relatedOffer.getSkuId());
            newReview.setSpuId(relatedOffer.getSpuId());
            newReview.setPlatformSpuId(relatedOffer.getPlatformSpuId());

            reviewsToInsert.add(newReview);
        }

        //批量插入
        if (!reviewsToInsert.isEmpty()) {
            reviewsService.saveBatch(reviewsToInsert);
            log.info("成功批量插入 {} 条新评论。", reviewsToInsert.size());
        }
    }

    /**
     * 对新数据进行型号预处理
     */
    private void preprocessModels(List<ProductDataDTO> dtoList) {
        log.info("开始对新数据进行型号预处理，数据量：{}", dtoList.size());

        ProcessingConfig config = ProcessingConfig.getDefault();
        int processedCount = 0;

        for (ProductDataDTO dto : dtoList) {
            try {
                if (StringUtils.hasText(dto.getModel()) && StringUtils.hasText(dto.getBrand())) {
                    String processedModel = modelProcessor.processModelString(
                            dto.getModel(), dto.getBrand(), config);

                    if (StringUtils.hasText(processedModel) && !processedModel.equals(dto.getModel())) {
                        dto.setProcessedModel(processedModel);
                        log.debug("型号预处理完成，原始: {}, 处理后: {}", dto.getModel(), processedModel);
                        processedCount++;
                    } else {
                        dto.setProcessedModel(dto.getModel());
                    }
                } else {
                    dto.setProcessedModel(dto.getModel());
                }
            } catch (Exception e) {
                log.warn("型号预处理失败，商品: {}, 使用原始型号, 错误: {}", dto.getModel(), e.getMessage());
                dto.setProcessedModel(dto.getModel());
            }
        }

        log.info("型号预处理完成，处理数量：{}/{}", processedCount, dtoList.size());
    }

    @Override
    @Transactional
    public void batchProcessPriceAndInventory(List<ProductPriceInventoryDTO> dtoList) {
        log.info("批量处理商品价格和库存数据，数据量：{}", dtoList.size());

        LocalDateTime startTime = LocalDateTime.now();
        int successCount = 0;
        int failedCount = 0;
        int notFoundCount = 0;

        try {
            for (ProductPriceInventoryDTO dto : dtoList) {
                try {
                    // 根据平台信息查找对应的offers记录
                    ProductDataOffers existingOffer = findOfferByPlatformInfo(dto);

                    // 如果这边不存在对应的offer记录，则认为是脏数据，日志记录处理
                    if (existingOffer == null) {
                        log.warn("UnifiedProductDataServiceImpl|batchProcessPriceAndInventory 未找到对应的offers记录，忽略处理: sourcePlatform={}, platformSpuId={}, platformSkuId={}",
                                dto.getSourcePlatform(), dto.getPlatformSpuId(), dto.getPlatformSkuId());
                        notFoundCount++;
                        continue;
                    }

                    // 处理价格和库存更新
                    boolean updated = processPriceAndInventoryUpdate(existingOffer, dto);

                    if (updated) {
                        successCount++;
                    } else {
                        log.debug("商品价格和库存无变化，跳过更新: offerId={}", existingOffer.getId());
                    }

                } catch (Exception e) {
                    log.error("处理商品价格和库存失败: sourcePlatform={}, platformSkuId={}, 错误: {}",
                            dto.getSourcePlatform(), dto.getPlatformSkuId(), e.getMessage());
                    failedCount++;
                }
            }

            LocalDateTime endTime = LocalDateTime.now();
            long duration = java.time.Duration.between(startTime, endTime).getSeconds();

            log.info("批量处理商品价格和库存完成，总数: {}, 成功: {}, 失败: {}, 未找到: {}, 耗时: {}秒",
                    dtoList.size(), successCount, failedCount, notFoundCount, duration);

        } catch (Exception e) {
            log.error("批量处理商品价格和库存失败", e);
            throw new RuntimeException("批量处理商品价格和库存失败：" + e.getMessage());
        }
    }

    /**
     * 根据平台信息查找offers记录
     */
    private ProductDataOffers findOfferByPlatformInfo(ProductPriceInventoryDTO dto) {
        LambdaQueryWrapper<ProductDataOffers> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProductDataOffers::getSourcePlatform, dto.getSourcePlatform())
                .eq(ProductDataOffers::getPlatformSpuId, dto.getPlatformSpuId())
                .eq(ProductDataOffers::getPlatformSkuId, dto.getPlatformSkuId());

        return offersService.getOne(queryWrapper);
    }

    /**
     * 处理价格和库存更新
     */
    private boolean processPriceAndInventoryUpdate(ProductDataOffers existingOffer, ProductPriceInventoryDTO dto) {
        // 保存更新前的数据用于历史记录
        ProductDataOffers oldOffer = new ProductDataOffers();
        BeanUtils.copyProperties(existingOffer, oldOffer);

        // 使用带字段追踪的更新方法
        boolean hasChanges = DataUpdateStrategy.updateOffersRecordByDataChannelWithTracking(existingOffer, dto);

        if (!hasChanges && dto.getForceUpdate() != 1) {
            log.info("商品价格和库存无变化，跳过更新: offerId={}", existingOffer.getId());
            return false;
        }

        // 保存更新
        String triggerSource = DataTriggerSource.BATCH_PROCESS.getCode();
        boolean success = offersService.saveOrUpdateWithPriceHistory(existingOffer, triggerSource);

        if (success) {
            // 记录库存变化历史
            inventoryChangeListener.recordInventoryChange(
                    oldOffer,
                    existingOffer,
                    DataChangeTypeEnum.UPDATE,
                    triggerSource
            );

            log.info("商品价格和库存更新成功: offerId={}, skuId={}",
                    existingOffer.getId(), existingOffer.getSkuId());
            return true;
        } else {
            log.error("商品价格和库存更新失败: offerId={}", existingOffer.getId());
            return false;
        }
    }

    /**
     * 更新offers字段
     */
    private boolean updateOfferFields(ProductDataOffers existingOffer, ProductPriceInventoryDTO dto) {
        boolean hasChanges = false;

        // 更新价格相关字段
        if (dto.getPrice() != null && !Objects.equals(existingOffer.getPrice(), dto.getPrice())) {
            existingOffer.setPrice(dto.getPrice());
            hasChanges = true;
        }

        if (dto.getListPrice() != null && !Objects.equals(existingOffer.getListPrice(), dto.getListPrice())) {
            existingOffer.setListPrice(dto.getListPrice());
            hasChanges = true;
        }

        if (dto.getDiscount() != null && !Objects.equals(existingOffer.getDiscount(), dto.getDiscount())) {
            existingOffer.setDiscount(dto.getDiscount());
            hasChanges = true;
        }

        // 更新库存相关字段
        if (dto.getInventory() != null && !Objects.equals(existingOffer.getInventory(), dto.getInventory())) {
            existingOffer.setInventory(dto.getInventory());
            hasChanges = true;
        }

        // 更新时间字段
        if (dto.getPriceUpdateTime() != null) {
            existingOffer.setPriceUpdateTime(dto.getPriceUpdateTime().toString());
            hasChanges = true;
        }

        if (dto.getInventoryUpdateTime() != null) {
            existingOffer.setInventoryUpdateTime(dto.getInventoryUpdateTime());
            hasChanges = true;
        }

        // 更新数据渠道
        if (dto.getDataChannel() != null) {
            existingOffer.setDataChannel(dto.getDataChannel());
        }

        return hasChanges;
    }
}

