package ai.pricefox.mallfox.service.system.impl;

import ai.pricefox.mallfox.common.util.JSONUtils;
import ai.pricefox.mallfox.mapper.system.ProductTableConfigMapper;
import ai.pricefox.mallfox.domain.product.ProductTableConfig;
import ai.pricefox.mallfox.model.param.ProductTableConfigRequest;
import ai.pricefox.mallfox.model.response.ProductTableConfigResponse;
import ai.pricefox.mallfox.service.system.ProductTableConfigService;
import ai.pricefox.mallfox.utils.LexoRankUtils;
import ai.pricefox.mallfox.vo.base.CommonResult;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 产品表格配置服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductTableConfigServiceImpl implements ProductTableConfigService {

    @Autowired
    private ProductTableConfigMapper productTableConfigMapper;

    @Override
    public CommonResult<List<ProductTableConfigResponse>> getConfigsByType(Integer type, String tag) {
        log.info("根据类型查询配置列表，type: {}", type);

        try {
            // 验证类型值
            if (type == null) {
                return CommonResult.error("类型不能为空");
            }
            if (type != 1 && type != 2) {
                return CommonResult.error("类型值无效，只能是1（字段）或2（配置）");
            }

            List<ProductTableConfig> configs = productTableConfigMapper.selectByTypeWithSort(type, tag);

            if (CollectionUtils.isEmpty(configs)) {
                log.info("未找到类型为 {} 的配置", type);
                return CommonResult.success(new ArrayList<>());
            }

            List<ProductTableConfigResponse> result = configs.stream()
                    .map(this::convertToResponseDTO)
                    .collect(Collectors.toList());

            result.forEach(item -> item.setInfo(JSONUtils.toJsonObject(item.getInfo())));

            log.info("查询到 {} 条配置记录", result.size());
            return CommonResult.success(result);

        } catch (Exception e) {
            log.error("根据类型查询配置列表失败，type: {}", type, e);
            return CommonResult.error("查询配置列表失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<ProductTableConfigResponse> updateByField(ProductTableConfigRequest.UpdateByFieldRequest request) {
        log.info("根据字段更新配置，field: {}", request.getField());

        try {
            // 验证参数
            if (request.getField() == null) {
                return CommonResult.error("字段不能为空");
            }
            if (request.getType() != null && request.getType() != 1 && request.getType() != 2) {
                return CommonResult.error("类型值无效，只能是1（字段）或2（配置）");
            }

            // 检查字段是否存在
            ProductTableConfig existingConfig = productTableConfigMapper.selectByField(request.getField());
            if (existingConfig == null) {
                log.warn("字段 {} 不存在，无法更新", request.getField());
                return CommonResult.error("字段不存在，无法更新");
            }

            // 执行更新
            int updateCount = productTableConfigMapper.updateByField(
                    request.getField(),
                    request.getType(),
                    request.getInfo(),
                    request.getWeight()
            );

            if (updateCount == 0) {
                log.warn("更新失败，field: {}", request.getField());
                return CommonResult.error("更新失败");
            }

            log.info("字段 {} 更新成功", request.getField());
            return CommonResult.success();

        } catch (Exception e) {
            log.error("根据字段更新配置失败，field: {}", request.getField(), e);
            return CommonResult.error("更新配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> batchInsert(List<ProductTableConfigRequest> configs) {
        log.info("批量插入配置，数量: {}", configs.size());

        try {
            // 验证配置列表
            if (CollectionUtil.isEmpty(configs)) {
                return CommonResult.error("配置列表不能为空");
            }

            if (configs.size() > 100) {
                return CommonResult.error("单次批量插入不能超过100条记录");
            }

            // 验证每个配置的类型值
            for (ProductTableConfigRequest config : configs) {
                if (config.getType() != null && config.getType() != 1 && config.getType() != 2) {
                    return CommonResult.error("类型值无效，只能是1（字段）或2（配置）");
                }
            }
            List<ProductTableConfig> configsToInsert = new ArrayList<>();
            // 验证和转换数据
            for (ProductTableConfigRequest configRequest : configs) {

                ProductTableConfig config = convertToEntity(configRequest);
                config.setCreateTime(LocalDateTime.now());
                configsToInsert.add(config);
            }

            // 批量插入
            if (!configsToInsert.isEmpty()) {
                int insertCount = productTableConfigMapper.batchInsert(configsToInsert);
                log.info("批量插入成功，插入数量: {}", insertCount);

            }
            return CommonResult.success();

        } catch (Exception e) {
            log.error("批量插入配置失败", e);
            return CommonResult.error("批量插入失败: " + e.getMessage());
        }
    }

    /**
     * 更新权重
     *
     * @param request
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> updateWeight(ProductTableConfigRequest.UpdateWeightRequest request) {
        try {
            if (request.getTag() == null) {
                return CommonResult.error("表格标识不能为空");
            }
            if (request.getId() == null) {
                return CommonResult.error("ID不能为空");
            }
            if (request.getTargetId() == null) {
                return CommonResult.error("目标ID不能为空");
            }

            String tag = request.getTag();
            Integer targetId = request.getTargetId();

            String newWeight;
            if (targetId == 0) {
                String maxWeight = productTableConfigMapper.selectMaxWeightByTag(tag);
                log.info("最高权重: {}", maxWeight);
                newWeight = LexoRankUtils.after(maxWeight);
            } else {
                String targetWeight = productTableConfigMapper.selectById(targetId);
                if (targetWeight == null) {
                    return CommonResult.error("目标ID不存在");
                }

                String prevWeight = productTableConfigMapper.selectPrevWeightByTag(tag, targetWeight);
                if (prevWeight != null && !prevWeight.isEmpty()) {
                    log.info("区间权重: {}", prevWeight);
                    newWeight = LexoRankUtils.between(prevWeight, targetWeight);
                } else {
                    log.info("最低权重: {}", targetWeight);
                    newWeight = LexoRankUtils.before(targetWeight);
                }
            }

            Integer updateId = request.getId();
            int rowsAffected = productTableConfigMapper.updateWeightById(updateId, newWeight);
            if (rowsAffected <= 0) {
                return CommonResult.error("权重更新失败");
            }

            log.info("计算新权重: {}", newWeight);
            return CommonResult.success(true);
        } catch (Exception e) {
            log.error("更新权重失败", e);
            return CommonResult.error("更新权重失败: " + e.getMessage());
        }
    }

    /**
     * 实体转换为响应DTO
     */
    private ProductTableConfigResponse convertToResponseDTO(ProductTableConfig entity) {
        if (entity == null) {
            return null;
        }

        ProductTableConfigResponse dto = new ProductTableConfigResponse();
        BeanUtils.copyProperties(entity, dto);
        if (StringUtils.isEmpty(entity.getInfo())) {
            dto.setInfo(new JSONObject());
        } else {
            dto.setInfo(JSONUtils.fromJson(entity.getInfo(), JSONObject.class));
        }
        // 设置类型描述
        ProductTableConfig.TypeEnum typeEnum = ProductTableConfig.TypeEnum.getByCode(entity.getType());
        if (typeEnum != null) {
            dto.setTypeDesc(typeEnum.getDesc());
        }

        return dto;
    }

    /**
     * 请求DTO转换为实体
     */
    private ProductTableConfig convertToEntity(ProductTableConfigRequest dto) {
        if (dto == null) {
            return null;
        }

        ProductTableConfig entity = new ProductTableConfig();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

}
