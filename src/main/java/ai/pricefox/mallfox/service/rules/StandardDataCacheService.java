package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.common.constant.RedisKeyConstants;
import ai.pricefox.mallfox.common.util.CacheUtil;
import ai.pricefox.mallfox.domain.standard.*;
import ai.pricefox.mallfox.service.standard.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.List;

/**
 * 标准化数据缓存服务
 * 负责将标准库和匹配规则数据缓存到Redis中，减少数据库查询
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StandardDataCacheService {

    private final CacheUtil cacheUtil;

    // 注入所有相关的服务
    private final InvalidValuePatternsService invalidValuePatternsService;
    private final NormalizationAliasService normalizationAliasService;
    private final NormalizationLibraryService normalizationLibraryService;
    private final NormalizationRulesService normalizationRulesService;
    private final PlatformCategoryMappingService platformCategoryMappingService;
    private final PlatformFieldMappingService platformFieldMappingService;
    private final StandardBrandService standardBrandService;
    private final StandardCategoryService standardCategoryService;
    private final StandardColorService standardColorService;
    private final StandardFieldService standardFieldService;
    private final StandardModelService standardModelService;
    private final StandardServiceProviderService standardServiceProviderService;
    private final StandardStorageService standardStorageService;
    private final TripartitePlatformService tripartitePlatformService;

    /**
     * 获取无效值模式列表
     *
     * @return 无效值模式列表
     */
    public List<InvalidValuePatterns> getInvalidValuePatterns() {
        String key = RedisKeyConstants.INVALID_VALUE_PATTERNS;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取无效值模式数据");
            return (List<InvalidValuePatterns>) cached;
        }

        log.debug("从数据库中获取无效值模式数据");
        List<InvalidValuePatterns> data = invalidValuePatternsService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取归一化别名列表
     *
     * @return 归一化别名列表
     */
    public List<NormalizationAlias> getNormalizationAlias() {
        String key = RedisKeyConstants.NORMALIZATION_ALIAS;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取归一化别名数据");
            return (List<NormalizationAlias>) cached;
        }

        log.debug("从数据库中获取归一化别名数据");
        List<NormalizationAlias> data = normalizationAliasService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取归一化值库列表
     *
     * @return 归一化值库列表
     */
    public List<NormalizationLibrary> getNormalizationLibrary() {
        String key = RedisKeyConstants.NORMALIZATION_LIBRARY;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取归一化值库数据");
            return (List<NormalizationLibrary>) cached;
        }

        log.debug("从数据库中获取归一化值库数据");
        List<NormalizationLibrary> data = normalizationLibraryService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取归一化规则列表
     *
     * @return 归一化规则列表
     */
    public List<NormalizationRules> getNormalizationRules() {
        String key = RedisKeyConstants.NORMALIZATION_RULES;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取归一化规则数据");
            return (List<NormalizationRules>) cached;
        }

        log.debug("从数据库中获取归一化规则数据");
        List<NormalizationRules> data = normalizationRulesService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取平台分类映射列表
     *
     * @return 平台分类映射列表
     */
    public List<PlatformCategoryMapping> getPlatformCategoryMapping() {
        String key = RedisKeyConstants.PLATFORM_CATEGORY_MAPPING;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取平台分类映射数据");
            return (List<PlatformCategoryMapping>) cached;
        }

        log.debug("从数据库中获取平台分类映射数据");
        List<PlatformCategoryMapping> data = platformCategoryMappingService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取平台字段映射列表
     *
     * @return 平台字段映射列表
     */
    public List<PlatformFieldMapping> getPlatformFieldMapping() {
        String key = RedisKeyConstants.PLATFORM_FIELD_MAPPING;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取平台字段映射数据");
            return (List<PlatformFieldMapping>) cached;
        }

        log.debug("从数据库中获取平台字段映射数据");
        List<PlatformFieldMapping> data = platformFieldMappingService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准品牌列表
     *
     * @return 标准品牌列表
     */
    public List<StandardBrand> getStandardBrand() {
        String key = RedisKeyConstants.STANDARD_BRAND;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准品牌数据");
            return (List<StandardBrand>) cached;
        }

        log.debug("从数据库中获取标准品牌数据");
        List<StandardBrand> data = standardBrandService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准分类列表
     *
     * @return 标准分类列表
     */
    public List<StandardCategory> getStandardCategory() {
        String key = RedisKeyConstants.STANDARD_CATEGORY;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准分类数据");
            return (List<StandardCategory>) cached;
        }

        log.debug("从数据库中获取标准分类数据");
        List<StandardCategory> data = standardCategoryService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准颜色列表
     *
     * @return 标准颜色列表
     */
    public List<StandardColor> getStandardColor() {
        String key = RedisKeyConstants.STANDARD_COLOR;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准颜色数据");
            return (List<StandardColor>) cached;
        }

        log.debug("从数据库中获取标准颜色数据");
        List<StandardColor> data = standardColorService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准字段列表
     *
     * @return 标准字段列表
     */
    public List<StandardField> getStandardField() {
        String key = RedisKeyConstants.STANDARD_FIELD;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准字段数据");
            return (List<StandardField>) cached;
        }

        log.debug("从数据库中获取标准字段数据");
        List<StandardField> data = standardFieldService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准型号列表
     *
     * @return 标准型号列表
     */
    public List<StandardModel> getStandardModel() {
        String key = RedisKeyConstants.STANDARD_MODEL;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准型号数据");
            return (List<StandardModel>) cached;
        }

        log.debug("从数据库中获取标准型号数据");
        List<StandardModel> data = standardModelService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准服务商列表
     *
     * @return 标准服务商列表
     */
    public List<StandardServiceProvider> getStandardServiceProvider() {
        String key = RedisKeyConstants.STANDARD_SERVICE_PROVIDER;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准服务商数据");
            return (List<StandardServiceProvider>) cached;
        }

        log.debug("从数据库中获取标准服务商数据");
        List<StandardServiceProvider> data = standardServiceProviderService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准存储列表
     *
     * @return 标准存储列表
     */
    public List<StandardStorage> getStandardStorage() {
        String key = RedisKeyConstants.STANDARD_STORAGE;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取标准存储数据");
            return (List<StandardStorage>) cached;
        }

        log.debug("从数据库中获取标准存储数据");
        List<StandardStorage> data = standardStorageService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取第三方平台列表
     *
     * @return 第三方平台列表
     */
    public List<TripartitePlatform> getTripartitePlatform() {
        String key = RedisKeyConstants.TRIPARTITE_PLATFORM;
        Object cached = cacheUtil.getCache(key);
        if (cached != null) {
            log.debug("从缓存中获取第三方平台数据");
            return (List<TripartitePlatform>) cached;
        }

        log.debug("从数据库中获取第三方平台数据");
        List<TripartitePlatform> data = tripartitePlatformService.list();
        cacheUtil.setCache(key, data, RedisKeyConstants.STANDARD_DATA_TIMEOUT, java.util.concurrent.TimeUnit.SECONDS);
        return data;
    }

    /**
     * 获取标准颜色
     *
     * @param value 颜色名称
     * @return 标准颜色名称，如果不存在则返回null
     */
    public String getStandardColor(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        
        List<StandardColor> standardColors = getStandardColor();
        if (standardColors == null || standardColors.isEmpty()) {
            return null;
        }
        
        // 查找匹配的标准颜色
        for (StandardColor color : standardColors) {
            if (value.equalsIgnoreCase(color.getCodeNameLevel1()) || 
                value.equalsIgnoreCase(color.getCodeNameLevel2())) {
                return color.getCodeNameLevel2();
            }
        }
        
        return null; // 没有找到匹配的标准颜色
    }
    
    /**
     * 获取标准品牌
     *
     * @param value 品牌名称
     * @return 标准品牌名称，如果不存在则返回null
     */
    public String getStandardBrand(String value) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        
        List<StandardBrand> standardBrands = getStandardBrand();
        if (standardBrands == null || standardBrands.isEmpty()) {
            return null;
        }
        
        // 查找匹配的标准品牌
        for (StandardBrand brand : standardBrands) {
            if (value.equalsIgnoreCase(brand.getBrandNameEn()) || 
                value.equalsIgnoreCase(brand.getBrandNameCn())) {
                return brand.getBrandNameEn();
            }
        }
        
        return null; // 没有找到匹配的标准品牌
    }
    
    /**
     * 清除所有标准化数据缓存
     */
    public void clearAllStandardDataCache() {
        cacheUtil.deleteCache(RedisKeyConstants.INVALID_VALUE_PATTERNS);
        cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_ALIAS);
        cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_LIBRARY);
        cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_RULES);
        cacheUtil.deleteCache(RedisKeyConstants.PLATFORM_CATEGORY_MAPPING);
        cacheUtil.deleteCache(RedisKeyConstants.PLATFORM_FIELD_MAPPING);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_BRAND);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_CATEGORY);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_COLOR);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_FIELD);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_MODEL);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_SERVICE_PROVIDER);
        cacheUtil.deleteCache(RedisKeyConstants.STANDARD_STORAGE);
        cacheUtil.deleteCache(RedisKeyConstants.TRIPARTITE_PLATFORM);
        log.info("已清除所有标准化数据缓存");
    }

    /**
     * 监听实体保存事件，清除相关缓存
     * 只有当标准化相关的实体发生变化时才清除对应缓存
     */
    @EventListener
    public void handleEntitySaveEvent(Object event) {
        // 检查事件类型，只对标准化相关的实体进行缓存清除
        if (event instanceof ai.pricefox.mallfox.domain.standard.InvalidValuePatterns) {
            cacheUtil.deleteCache(RedisKeyConstants.INVALID_VALUE_PATTERNS);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.NormalizationAlias) {
            cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_ALIAS);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.NormalizationLibrary) {
            cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_LIBRARY);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.NormalizationRules) {
            cacheUtil.deleteCache(RedisKeyConstants.NORMALIZATION_RULES);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.PlatformCategoryMapping) {
            cacheUtil.deleteCache(RedisKeyConstants.PLATFORM_CATEGORY_MAPPING);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.PlatformFieldMapping) {
            cacheUtil.deleteCache(RedisKeyConstants.PLATFORM_FIELD_MAPPING);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardBrand) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_BRAND);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardCategory) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_CATEGORY);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardColor) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_COLOR);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardField) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_FIELD);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardModel) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_MODEL);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardServiceProvider) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_SERVICE_PROVIDER);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.StandardStorage) {
            cacheUtil.deleteCache(RedisKeyConstants.STANDARD_STORAGE);
        }
        if (event instanceof ai.pricefox.mallfox.domain.standard.TripartitePlatform) {
            cacheUtil.deleteCache(RedisKeyConstants.TRIPARTITE_PLATFORM);
        }
    }

}
