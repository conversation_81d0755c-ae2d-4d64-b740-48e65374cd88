package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.StandardizationRecord;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.domain.standard.mongo.RawDataService;
import ai.pricefox.mallfox.enums.StandardizationStepEnum;
import ai.pricefox.mallfox.event.DataStandardizedEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.service.standard.StandardizationRecordService;
import ai.pricefox.mallfox.service.standard.TripartitePlatformService;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 数据标准化服务
 *
 * <p>该服务使用Drools规则引擎处理不同平台的商品数据，将其转换为标准格式。</p>
 *
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>执行字段映射，将平台特定字段映射到标准字段</li>
 *   <li>应用Drools规则进行数据标准化处理</li>
 *   <li>整合各类标准化规则和数据（品类映射、归一化规则、标准库等）</li>
 * </ul>
 *
 * <p>工作流程：</p>
 * <ol>
 *   <li>首先进行字段映射，将平台数据转换为初步的标准数据结构</li>
 *   <li>初始化Drools规则引擎会话</li>
 *   <li>将数据和所有相关规则插入到规则引擎中</li>
 *   <li>执行规则引擎进行数据标准化处理</li>
 *   <li>返回标准化后的数据</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class DataStandardizationService {

    private final KieContainer kieContainer;
    private final StandardDataCacheService standardDataCacheService;
    private final FieldMappingService fieldMappingService;
    private final StandardizationRecordService standardizationRecordService;
    private final TripartitePlatformService tripartitePlatformService;
    private final RawDataService rawDataService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 将平台商品数据标准化为标准商品数据
     *
     * <p>这是数据标准化的核心方法，它会执行完整的标准化流程：</p>
     * <ol>
     *   <li>使用字段映射服务进行初步字段映射</li>
     *   <li>初始化Drools规则引擎会话</li>
     *   <li>将数据和规则插入规则引擎</li>
     *   <li>执行规则处理</li>
     *   <li>返回标准化结果</li>
     * </ol>
     *
     * @param productData 平台商品数据
     * @return 标准化后的商品数据
     */
    public DynamicStandardProduct standardizeProductData(ProductDataDTO productData) {
        if (productData == null || productData.getSourcePlatform() == null) {
            log.error("产品数据或平台信息为空");
            throw new IllegalArgumentException("产品数据和平台信息不能为空");
        }

        // 查询平台信息
        TripartitePlatform platform = tripartitePlatformService.getByPlatformCode(productData.getSourcePlatform(), productData.getDataChannel());
        if (platform == null) {
            log.error("未找到平台信息: sourcePlatform={}, dataChannel={}", productData.getSourcePlatform(), productData.getDataChannel());
            throw new IllegalArgumentException("未找到平台信息");
        }
        productData.setPlatformCode(platform.getPlatformCode());
        log.debug("开始数据标准化过程: sourcePlatform:{}, platformSkuId:{}", productData.getSourcePlatform(), productData.getPlatformSkuId());
        long startTime = System.currentTimeMillis();
        Gson gson = new Gson();

        // 保存源数据到mongo
        String dataId = rawDataService.saveRawData(platform.getPlatformCode(), generateProductIdentifier(productData), gson.toJson(productData));
        // 先进行字段映射
        DynamicStandardProduct standardProduct = fieldMappingService.mapFields(productData);
        log.debug("字段映射完成");
        log.debug("字段映射后的standardProduct: {}", standardProduct.getFields());

        // 初始化Drools会话
        KieSession kieSession = null;
        try {
            // 直接使用已配置的命名会话
            kieSession = kieContainer.newKieSession("ksession-rules");
            log.debug("成功创建命名KieSession: ksession-rules");

            // 如果仍然无法创建KieSession，直接返回标准产品
            if (kieSession == null) {
                log.warn("警告: 无法创建KieSession，跳过规则处理");
                return standardProduct;
            }

            // 将数据插入到规则引擎中
            kieSession.insert(productData);
            kieSession.insert(standardProduct);

            // 插入所有相关规则和映射数据
            insertAllRulesAndData(kieSession);

            // 执行规则前记录状态
            log.debug("执行规则前的standardProduct: {}", standardProduct.getFields());

            // 执行规则
            int firedRules = kieSession.fireAllRules();
            log.debug("数据标准化完成，共执行 {} 条规则", firedRules);

            // 执行规则后记录状态
            log.debug("执行规则后的standardProduct: {}", standardProduct.getFields());

        } catch (Exception e) {
            log.error("规则执行过程中发生错误", e);
        } finally {
            // 确保在kieSession不为null时才调用dispose()
            if (kieSession != null) {
                kieSession.dispose();
                log.debug("KieSession已释放");
            }
        }

        // 记录标准化过程
        try {
            StandardizationRecord record = standardizationRecordService.queryByDataId(dataId);
            record = record == null ? new StandardizationRecord() : record;
            record.setPlatformCode(productData.getPlatformCode());
            record.setSkuId(productData.getPlatformSkuId());
            record.setStepType(StandardizationStepEnum.INITIALIZATION);
            record.setDataId(dataId);
            record.setBeforeData(gson.toJson(productData));
            // 字段标准化已移到规则中，直接使用standardProduct.getFields()
            record.setAfterData(gson.toJson(standardProduct.getFields()));
            record.setChangeDetails("完成数据标准化处理");
            record.setOperator("SYSTEM");
            record.setCreateDate(new Date());
            standardizationRecordService.saveOrUpdate(record);
        } catch (Exception e) {
            log.error("记录标准化过程时出错", e);
        }

        // 发布标准化完成事件
        eventPublisher.publishEvent(new DataStandardizedEvent(this, productData.getPlatformSkuId(), productData.getPlatformCode(), "STANDARDIZATION", gson.toJson(productData), gson.toJson(standardProduct.getFields()), "数据标准化完成"));
        long endTime = System.currentTimeMillis();
        log.info("数据标准化过程完成，耗时: {} ms", endTime - startTime);
        return standardProduct;
    }

    /**
     * 生成产品标识符
     *
     * <p>产品标识符由平台代码和SKU ID组成，用于唯一标识一个产品。</p>
     *
     * @param productData 产品数据
     * @return 产品标识符，格式为"平台代码_渠道_SKU ID"
     */
    private String generateProductIdentifier(ProductDataDTO productData) {
        if (productData.getSourcePlatform() == null || productData.getDataChannel() == null || productData.getPlatformSkuId() == null) {
            return "unknown_unknown_unknown";
        }
        return productData.getSourcePlatform() + "_" + productData.getDataChannel() + "_" + productData.getPlatformSkuId();
    }

    /**
     * 插入所有规则和数据到规则引擎
     *
     * <p>将所有需要的规则和数据一次性插入到规则引擎中，包括：</p>
     * <ul>
     *   <li>平台品类映射规则</li>
     *   <li>归一化规则</li>
     *   <li>无效值模式</li>
     *   <li>各类标准库数据</li>
     * </ul>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertAllRulesAndData(KieSession kieSession) {
        log.debug("开始插入规则和数据到规则引擎");

        // 插入平台字段映射规则
        try {
            List<?> platformFieldMappings = standardDataCacheService.getPlatformFieldMapping();
            platformFieldMappings.forEach(kieSession::insert);
            log.trace("插入 {} 条平台字段映射规则", platformFieldMappings.size());
        } catch (Exception e) {
            log.error("插入平台字段映射规则时出错", e);
        }

        // 插入平台品类映射规则
        insertCategoryMappingRules(kieSession);

        // 插入归一化规则
        insertNormalizationRules(kieSession);

        // 插入无效值模式
        insertInvalidValuePatterns(kieSession);

        // 插入标准库数据
        insertStandardLibraries(kieSession);

        log.debug("规则和数据插入完成");
    }

    /**
     * 插入平台品类映射规则到规则引擎
     *
     * <p>平台品类映射规则用于将不同平台的品类映射到统一的标准品类体系。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertCategoryMappingRules(KieSession kieSession) {
        // 获取所有平台品类映射规则
        try {
            List<?> categoryMappings = standardDataCacheService.getPlatformCategoryMapping();
            categoryMappings.forEach(kieSession::insert);
            log.trace("插入 {} 条平台品类映射规则", categoryMappings.size());
        } catch (Exception e) {
            log.error("插入平台品类映射规则时出错", e);
        }
    }

    /**
     * 插入归一化规则到规则引擎
     *
     * <p>归一化规则用于将不同格式的数据统一为标准格式，例如将"128 GB"、"128GB"等统一为"128GB"。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertNormalizationRules(KieSession kieSession) {
        // 获取所有归一化规则
        try {
            List<?> normalizationRules = standardDataCacheService.getNormalizationRules();
            normalizationRules.forEach(kieSession::insert);

            List<?> normalizationLibraries = standardDataCacheService.getNormalizationLibrary();
            normalizationLibraries.forEach(kieSession::insert);

            log.trace("插入 {} 条归一化规则和 {} 条归一化库数据", normalizationRules.size(), normalizationLibraries.size());
        } catch (Exception e) {
            log.error("插入归一化规则时出错", e);
        }
    }

    /**
     * 插入无效值模式到规则引擎
     *
     * <p>无效值模式用于识别和处理无效或错误的数据值。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertInvalidValuePatterns(KieSession kieSession) {
        // 获取所有无效值模式
        try {
            List<?> invalidValuePatterns = standardDataCacheService.getInvalidValuePatterns();
            invalidValuePatterns.forEach(kieSession::insert);
            log.trace("插入 {} 条无效值模式", invalidValuePatterns.size());
        } catch (Exception e) {
            log.error("插入无效值模式时出错", e);
        }
    }

    /**
     * 插入标准库数据到规则引擎
     *
     * <p>标准库数据包括标准品牌、标准品类、标准颜色等，用于数据标准化过程中的参考。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertStandardLibraries(KieSession kieSession) {
        // 插入所有标准库数据
        try {
            // 定义标准库服务和描述信息
            // 定义标准库服务和描述信息
            List<StandardLibraryInfo> standardLibraries = Arrays.asList(new StandardLibraryInfo(standardDataCacheService.getStandardBrand(), "品牌"), new StandardLibraryInfo(standardDataCacheService.getStandardCategory(), "品类"), new StandardLibraryInfo(standardDataCacheService.getStandardColor(), "颜色"), new StandardLibraryInfo(standardDataCacheService.getStandardModel(), "型号"), new StandardLibraryInfo(standardDataCacheService.getStandardStorage(), "存储"), new StandardLibraryInfo(standardDataCacheService.getStandardServiceProvider(), "服务商"), new StandardLibraryInfo(standardDataCacheService.getStandardField(), "字段"));

            // 插入所有标准库数据
            standardLibraries.forEach(libInfo -> {
                libInfo.getData().forEach(kieSession::insert);
                log.trace("插入 {} 条标准{}库数据", libInfo.getData().size(), libInfo.getDescription());
            });

        } catch (Exception e) {
            log.error("插入标准库数据时出错", e);
        }
    }

    /**
     * 标准库信息内部类
     * 用于封装标准库数据和服务描述
     */
    @Getter
    private static class StandardLibraryInfo {
        private final List<?> data;
        private final String description;

        public StandardLibraryInfo(List<?> data, String description) {
            this.data = data;
            this.description = description;
        }

    }

}