package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.domain.standard.mongo.RawDataService;
import ai.pricefox.mallfox.enums.RuleGroup;
import ai.pricefox.mallfox.enums.StandardizationSubStepEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.event.StandardizationStepEvent;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.service.standard.TripartitePlatformService;
import com.google.gson.Gson;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 数据标准化服务
 *
 * <p>该服务使用Drools规则引擎处理不同平台的商品数据，将其转换为标准格式。</p>
 *
 * <p>主要功能包括：</p>
 * <ul>
 *   <li>执行字段映射，将平台特定字段映射到标准字段</li>
 *   <li>应用Drools规则进行数据标准化处理</li>
 *   <li>整合各类标准化规则和数据（品类映射、归一化规则、标准库等）</li>
 * </ul>
 *
 * <p>工作流程：</p>
 * <ol>
 *   <li>首先进行字段映射，将平台数据转换为初步的标准数据结构</li>
 *   <li>初始化Drools规则引擎会话</li>
 *   <li>将数据和所有相关规则插入到规则引擎中</li>
 *   <li>执行规则引擎进行数据标准化处理</li>
 *   <li>返回标准化后的数据</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class DataStandardizationService {

    private final KieContainer kieContainer;
    private final StandardDataCacheService standardDataCacheService;
    private final TripartitePlatformService tripartitePlatformService;
    private final ApplicationEventPublisher eventPublisher;

    /**
     * 启动分步标准化流程
     *
     * @param productDataMap 平台商品数据
     */
    public void startStepByStepStandardizationWithJson(Map<String, Object> productDataMap) {
        if (productDataMap == null || !productDataMap.containsKey("sourcePlatform")) {
            log.error("[分步标准化] 产品数据或平台信息为空");
            throw new IllegalArgumentException("产品数据和平台信息不能为空");
        }

        log.info("[分步标准化] 开始启动分步标准化流程: sourcePlatform={}", productDataMap.get("sourcePlatform"));

        // 查询平台信息
        Object sourcePlatformObj = productDataMap.get("sourcePlatform");
        Object dataChannelObj = productDataMap.get("dataChannel");

        if (!(sourcePlatformObj instanceof String) || !(dataChannelObj instanceof String)) {
            log.error("[分步标准化] 平台信息类型错误");
            throw new IllegalArgumentException("平台信息必须是字符串类型");
        }

        ProductPlatformEnum sourcePlatform = ProductPlatformEnum.valueOf((String) sourcePlatformObj);
        DataChannelEnum dataChannel = DataChannelEnum.valueOf((String) dataChannelObj);

        TripartitePlatform platform = tripartitePlatformService.getByPlatformCode(sourcePlatform, dataChannel);
        if (platform == null) {
            log.error("[分步标准化] 未找到平台信息: sourcePlatform={}, dataChannel={}", sourcePlatform, dataChannel);
            throw new IllegalArgumentException("未找到平台信息");
        }
        productDataMap.put("platformCode", platform.getPlatformCode());
        log.debug("[分步标准化] 平台信息查询完成: platformCode={},platformName={},type={}", platform.getPlatformCode(),platform.getPlatformName(),platform.getSourceType());

        // 发布字段映射完成事件，触发下一步处理
        log.info("[分步标准化] 发布字段映射完成事件，触发下一步处理");
        eventPublisher.publishEvent(new StandardizationStepEvent(this, null, platform.getPlatformCode(), StandardizationSubStepEnum.MARGE_DATA, productDataMap, new DynamicStandardProduct(), null));
        log.info("[分步标准化] 字段映射事件发布完成");
    }

    /**
     * 执行单个标准化步骤
     *
     * @param step            步骤类型
     * @param originalDataMap 原始数据
     * @param currentData     当前数据
     * @param platformCode    平台代码
     * @return 处理后的数据
     */
    public DynamicStandardProduct executeStandardizationStepWithJson(StandardizationSubStepEnum step, Map<String, Object> originalDataMap, DynamicStandardProduct currentData, String platformCode) {

        final String LOG_PREFIX = "[标准化步骤] ";

        if (log.isInfoEnabled()) {
            log.info(LOG_PREFIX + "开始执行步骤: {} ({})", step.getDescription(), step.getCode());
        }

        KieSession kieSession = null;
        try {
            kieSession = kieContainer.newKieSession("ksession-rules");
            if (kieSession == null) {
                if (log.isWarnEnabled()) {
                    log.warn(LOG_PREFIX + "无法创建KieSession，跳过规则处理");
                }
                return currentData;
            }

            // 插入数据到规则引擎
            if (log.isDebugEnabled()) {
                log.debug(LOG_PREFIX + "插入原始数据到规则引擎");
            }
            kieSession.insert(originalDataMap);

            if (log.isDebugEnabled()) {
                log.debug(LOG_PREFIX + "插入当前数据到规则引擎");
            }
            kieSession.insert(currentData);

            if (log.isDebugEnabled()) {
                log.debug(LOG_PREFIX + "插入所有规则和数据到规则引擎");
            }
            insertAllRulesAndData(kieSession);

            // 执行对应规则组
            if (log.isInfoEnabled()) {
                log.info(LOG_PREFIX + "开始执行 {} 规则", step.getDescription());
            }

            int firedRules = executeRulesByStep(kieSession, step);

            if (log.isInfoEnabled()) {
                log.info(LOG_PREFIX + "步骤 {} 完成，执行 {} 条规则", step.getDescription(), firedRules);
            }

        } catch (Exception e) {
            if (log.isErrorEnabled()) {
                log.error(LOG_PREFIX + "规则执行错误", e);
            }
        } finally {
            if (kieSession != null) {
                if (log.isDebugEnabled()) {
                    log.debug(LOG_PREFIX + "释放KieSession资源");
                }
                kieSession.dispose();
            }
        }

        return currentData;
    }

    /**
     * 根据步骤类型执行对应的规则
     *
     * @param kieSession Kie会话
     * @param step       步骤类型
     * @return 执行的规则数量
     */
    private int executeRulesByStep(KieSession kieSession, StandardizationSubStepEnum step) {
        int firedRules = 0;

        switch (step) {
            case MARGE_DATA:
                // 执行字段映射和字段标准化规则
                log.info("[标准化步骤] 执行字段映射规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.FIELD_MAPPING));
                break;

            case CATEGORY_STANDARDIZATION:
                // 执行品类标准化规则
                log.info("[标准化步骤] 执行品类标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.CATEGORY));
                break;

            case BRAND_STANDARDIZATION:
                // 执行品牌标准化规则
                log.info("[标准化步骤] 执行品牌标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.BRAND));
                break;

            case MODEL_STANDARDIZATION:
                // 执行型号标准化规则
                log.info("[标准化步骤] 执行型号标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.MODEL));
                break;

            case COLOR_STANDARDIZATION:
                // 执行颜色标准化规则
                log.info("[标准化步骤] 执行颜色标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.COLOR));
                break;

            case STORAGE_STANDARDIZATION:
                // 执行存储标准化规则
                log.info("[标准化步骤] 执行存储标准化规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.STORAGE));
                break;

            case INDISTINGUISHABLE_PROCESSING:
                // 执行不可比处理规则
                log.info("[标准化步骤] 执行不可比处理规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.INDISTINGUISHABLE));
                break;

            case INVALID_VALUE_PROCESSING:
                // 执行无效值处理规则
                log.info("[标准化步骤] 执行无效值处理规则");
                firedRules += kieSession.fireAllRules(createRuleFilter(RuleGroup.INVALID_VALUE));
                break;

            default:
                // 默认执行所有规则
                log.info("[标准化步骤] 执行所有规则");
                firedRules += kieSession.fireAllRules();
                break;
        }

        return firedRules;
    }

    /**
     * 创建规则过滤器
     *
     * @param ruleGroup 规则组
     * @return 规则过滤器
     */
    private org.kie.api.runtime.rule.AgendaFilter createRuleFilter(RuleGroup ruleGroup) {
        return new org.kie.api.runtime.rule.AgendaFilter() {
            @Override
            public boolean accept(org.kie.api.runtime.rule.Match match) {
                String ruleName = match.getRule().getName();

                // 检查规则名称是否完全匹配任何指定的规则名称
                for (String targetRuleName : ruleGroup.getRuleNames()) {
                    if (ruleName.equals(targetRuleName)) {
                        return true;
                    }
                }

                return false;
            }
        };
    }

    /**
     * 发布事件的便捷方法
     *
     * @param event 事件对象
     */
    public void publishEvent(Object event) {
        log.debug("[事件发布] 发布事件: {}", event.getClass().getSimpleName());
        eventPublisher.publishEvent(event);
        log.debug("[事件发布] 事件发布完成: {}", event.getClass().getSimpleName());
    }

    /**
     * 处理并转发事件
     *
     * @param event           当前步骤事件
     * @param nextStep        下一步骤枚举
     * @param currentStepName 当前步骤名称（用于日志）
     * @param nextStepName    下一步骤名称（用于日志）
     */
    public void processAndForwardEvent(StandardizationStepEvent event, StandardizationSubStepEnum nextStep, String currentStepName, String nextStepName) {

        // 检查原始数据是否为空，如果为空则不进行任何处理
        if (event.getOriginalDataMap() == null) {
            log.warn("[标准化步骤] 原始数据为空，无法执行{}步骤", currentStepName);
            return;
        }

        // 使用JSON格式的原始数据
        DynamicStandardProduct result = executeStandardizationStepWithJson(nextStep, event.getOriginalDataMap(), event.getCurrentData(), event.getPlatformCode());

        log.debug("[标准化步骤] {}步骤后的数据: {}", currentStepName, result.getFields());
        log.info("[标准化步骤] {}步骤处理完成，准备进入{}步骤: skuId={}, platformCode={}", currentStepName, nextStepName, event.getSkuId(), event.getPlatformCode());

        // 发布下一步骤完成事件
        StandardizationStepEvent nextEvent = new StandardizationStepEvent(this, event.getSkuId(), event.getPlatformCode(), nextStep, event.getOriginalDataMap(), result, event.getDataId());

        // 发布下一个步骤事件
        publishEvent(nextEvent);
    }

    /**
     * 生成产品标识符
     *
     * <p>产品标识符由平台代码和SKU ID组成，用于唯一标识一个产品。</p>
     *
     * @param skuId               产品sku Id
     * @param dataChannelEnum     渠道
     * @param productPlatformEnum 平台
     * @return 产品标识符，格式为"平台代码_渠道_SKU ID"
     */
    public String generateProductIdentifierFromMap(String skuId, DataChannelEnum dataChannelEnum, ProductPlatformEnum productPlatformEnum) {
        if (productPlatformEnum == null || dataChannelEnum == null) {
            return "unknown_unknown_unknown";
        }

        return productPlatformEnum + "_" + dataChannelEnum + "_" + skuId;
    }


    /**
     * 插入所有规则和数据到规则引擎
     *
     * <p>将所有需要的规则和数据一次性插入到规则引擎中，包括：</p>
     * <ul>
     *   <li>平台品类映射规则</li>
     *   <li>归一化规则</li>
     *   <li>无效值模式</li>
     *   <li>各类标准库数据</li>
     * </ul>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertAllRulesAndData(KieSession kieSession) {
        log.debug("[规则引擎] 开始插入规则和数据到规则引擎");

        // 插入平台字段映射规则
        try {
            log.debug("[规则引擎] 插入平台字段映射规则");
            List<?> platformFieldMappings = standardDataCacheService.getPlatformFieldMapping();
            platformFieldMappings.forEach(kieSession::insert);
            log.trace("[规则引擎] 插入 {} 条平台字段映射规则", platformFieldMappings.size());
        } catch (Exception e) {
            log.error("[规则引擎] 插入平台字段映射规则时出错", e);
        }

        // 插入平台品类映射规则
        log.debug("[规则引擎] 插入平台品类映射规则");
        insertCategoryMappingRules(kieSession);

        // 插入归一化规则
        log.debug("[规则引擎] 插入归一化规则");
        insertNormalizationRules(kieSession);

        // 插入无效值模式
        log.debug("[规则引擎] 插入无效值模式");
        insertInvalidValuePatterns(kieSession);

        // 插入标准库数据
        log.debug("[规则引擎] 插入标准库数据");
        insertStandardLibraries(kieSession);

        log.debug("[规则引擎] 规则和数据插入完成");
    }

    /**
     * 插入平台品类映射规则到规则引擎
     *
     * <p>平台品类映射规则用于将不同平台的品类映射到统一的标准品类体系。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertCategoryMappingRules(KieSession kieSession) {
        // 获取所有平台品类映射规则
        try {
            log.debug("[规则引擎] 获取平台品类映射规则");
            List<?> categoryMappings = standardDataCacheService.getPlatformCategoryMapping();
            categoryMappings.forEach(kieSession::insert);
            log.trace("[规则引擎] 插入 {} 条平台品类映射规则", categoryMappings.size());
        } catch (Exception e) {
            log.error("[规则引擎] 插入平台品类映射规则时出错", e);
        }
    }

    /**
     * 插入归一化规则到规则引擎
     *
     * <p>归一化规则用于将不同格式的数据统一为标准格式，例如将"128 GB"、"128GB"等统一为"128GB"。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertNormalizationRules(KieSession kieSession) {
        // 获取所有归一化规则
        try {
            log.debug("[规则引擎] 获取归一化规则");
            List<?> normalizationRules = standardDataCacheService.getNormalizationRules();
            normalizationRules.forEach(kieSession::insert);

            List<?> normalizationLibraries = standardDataCacheService.getNormalizationLibrary();
            normalizationLibraries.forEach(kieSession::insert);

            log.trace("[规则引擎] 插入 {} 条归一化规则和 {} 条归一化库数据", normalizationRules.size(), normalizationLibraries.size());
        } catch (Exception e) {
            log.error("[规则引擎] 插入归一化规则时出错", e);
        }
    }

    /**
     * 插入无效值模式到规则引擎
     *
     * <p>无效值模式用于识别和处理无效或错误的数据值。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertInvalidValuePatterns(KieSession kieSession) {
        // 获取所有无效值模式
        try {
            log.debug("[规则引擎] 获取无效值模式");
            List<?> invalidValuePatterns = standardDataCacheService.getInvalidValuePatterns();
            invalidValuePatterns.forEach(kieSession::insert);
            log.trace("[规则引擎] 插入 {} 条无效值模式", invalidValuePatterns.size());
        } catch (Exception e) {
            log.error("[规则引擎] 插入无效值模式时出错", e);
        }
    }

    /**
     * 插入标准库数据到规则引擎
     *
     * <p>标准库数据包括标准品牌、标准品类、标准颜色等，用于数据标准化过程中的参考。</p>
     *
     * @param kieSession 规则引擎会话
     */
    private void insertStandardLibraries(KieSession kieSession) {
        // 插入所有标准库数据
        try {
            log.debug("[规则引擎] 获取标准库数据");
            // 定义标准库服务和描述信息
            List<StandardLibraryInfo> standardLibraries = Arrays.asList(new StandardLibraryInfo(standardDataCacheService.getStandardBrand(), "品牌"), new StandardLibraryInfo(standardDataCacheService.getStandardCategory(), "品类"), new StandardLibraryInfo(standardDataCacheService.getStandardColor(), "颜色"), new StandardLibraryInfo(standardDataCacheService.getStandardModel(), "型号"), new StandardLibraryInfo(standardDataCacheService.getStandardStorage(), "存储"), new StandardLibraryInfo(standardDataCacheService.getStandardServiceProvider(), "服务商"), new StandardLibraryInfo(standardDataCacheService.getStandardField(), "字段"));

            // 插入所有标准库数据
            standardLibraries.forEach(libInfo -> {
                libInfo.getData().forEach(kieSession::insert);
                log.trace("[规则引擎] 插入 {} 条标准{}库数据", libInfo.getData().size(), libInfo.getDescription());
            });

        } catch (Exception e) {
            log.error("[规则引擎] 插入标准库数据时出错", e);
        }
    }

    /**
     * 标准库信息内部类
     * 用于封装标准库数据和服务描述
     */
    @Getter
    private static class StandardLibraryInfo {
        private final List<?> data;
        private final String description;

        public StandardLibraryInfo(List<?> data, String description) {
            this.data = data;
            this.description = description;
        }

    }

}