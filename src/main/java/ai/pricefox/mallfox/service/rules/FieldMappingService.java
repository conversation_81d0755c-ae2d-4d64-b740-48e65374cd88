package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 字段映射服务类
 *
 * <p>该服务负责处理平台字段到标准字段的映射逻辑，包括：</p>
 * <ul>
 *   <li>根据平台代码获取字段映射关系</li>
 *   <li>将平台数据映射到标准产品</li>
 *   <li>管理字段映射关系的缓存</li>
 * </ul>
 *
 * <p>主要工作流程：</p>
 * <ol>
 *   <li>根据平台代码从数据库获取平台字段与标准字段的映射关系</li>
 *   <li>使用映射关系将平台数据转换为标准产品数据</li>
 *   <li>通过缓存机制提高性能，避免重复查询数据库</li>
 * </ol>
 *
 * <p>使用示例：</p>
 * <pre>
 * {@code
 * ProductDataDTO productData = createProductData();
 * DynamicStandardProduct standardProduct = fieldMappingService.mapFields(productData);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class FieldMappingService {

    // 标准数据缓存服务
    private final StandardDataCacheService standardDataCacheService;

    /**
     * 根据平台代码获取字段映射关系
     *
     * <p>此方法会从数据库查询平台字段与标准字段的映射关系，
     * 并通过StandardDataCacheService的Redis缓存机制提高性能。</p>
     *
     * @param platformCode 平台代码
     * @return 源字段到标准字段的映射关系，key为源字段名，value为标准字段对象
     */
    public Map<String, StandardField> getFieldMappingByPlatform(String platformCode) {
        log.debug("[字段映射服务] 开始获取平台 {} 的字段映射关系", platformCode);

        // 添加空值检查
        if (platformCode == null) {
            log.error("[字段映射服务] 平台代码为空，无法获取字段映射关系");
            throw new IllegalArgumentException("platformCode不能为空");
        }

        log.info("[字段映射服务] 为平台 {} 构建字段映射关系", platformCode);

        // 从缓存服务获取平台字段映射关系
        List<PlatformFieldMapping> platformMappings = standardDataCacheService.getPlatformFieldMapping();
        if (platformMappings == null) {
            log.warn("[字段映射服务] 平台字段映射数据为空，平台：{}", platformCode);
            return Collections.emptyMap();
        }

        log.debug("[字段映射服务] 获取到平台字段映射数据 {} 条", platformMappings.size());

        List<PlatformFieldMapping> filteredMappings = platformMappings.stream().filter(mapping -> platformCode.equals(mapping.getPlatformCode())).toList();
        log.debug("[字段映射服务] 过滤后平台 {} 的字段映射数据 {} 条", platformCode, filteredMappings.size());

        // 获取所有标准字段并建立code到字段的映射
        List<StandardField> standardFields = standardDataCacheService.getStandardField();
        log.debug("[字段映射服务] 获取到标准字段数据 {} 条", standardFields.size());

        Map<String, StandardField> standardFieldMap = standardFields.stream().collect(Collectors.toMap(StandardField::getFieldCode, field -> field));
        log.debug("[字段映射服务] 构建标准字段映射完成，共 {} 个标准字段", standardFieldMap.size());

        // 建立平台字段到标准字段的映射
        Map<String, StandardField> fieldMapping = filteredMappings.stream().filter(mapping -> standardFieldMap.containsKey(mapping.getStandardFieldCode())).collect(Collectors.toMap(PlatformFieldMapping::getSourceFieldName, mapping -> standardFieldMap.get(mapping.getStandardFieldCode()), (existing, replacement) -> {
            log.warn("[字段映射服务] 发现重复的平台字段映射: 平台字段'{}'同时映射到标准字段'{}({})'和'{}({})'，使用新的映射关系", existing.getFieldNameCn(), existing.getFieldNameCn(), existing.getFieldCode(), replacement.getFieldNameCn(), replacement.getFieldCode());
            return replacement;
        }));

        log.debug("[字段映射服务] 平台 {} 的字段映射关系构建完成，共 {} 个映射关系", platformCode, fieldMapping.size());

        return fieldMapping;
    }
}