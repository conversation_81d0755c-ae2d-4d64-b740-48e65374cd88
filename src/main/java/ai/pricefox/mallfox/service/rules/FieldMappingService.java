package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 字段映射服务类
 *
 * <p>该服务负责处理平台字段到标准字段的映射逻辑，包括：</p>
 * <ul>
 *   <li>根据平台代码获取字段映射关系</li>
 *   <li>将平台数据映射到标准产品</li>
 *   <li>管理字段映射关系的缓存</li>
 * </ul>
 *
 * <p>主要工作流程：</p>
 * <ol>
 *   <li>根据平台代码从数据库获取平台字段与标准字段的映射关系</li>
 *   <li>使用映射关系将平台数据转换为标准产品数据</li>
 *   <li>通过缓存机制提高性能，避免重复查询数据库</li>
 * </ol>
 *
 * <p>使用示例：</p>
 * <pre>
 * {@code
 * ProductDataDTO productData = createProductData();
 * DynamicStandardProduct standardProduct = fieldMappingService.mapFields(productData);
 * }
 * </pre>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class FieldMappingService {

    // 标准数据缓存服务
    private final StandardDataCacheService standardDataCacheService;

    /**
     * 缓存字段映射关系以提高性能
     * 使用ConcurrentHashMap保证线程安全性
     */
    private final Map<String, Map<String, StandardField>> fieldMappingCache = new ConcurrentHashMap<>();

    /**
     * 根据平台代码获取字段映射关系
     *
     * <p>此方法会首先尝试从缓存中获取映射关系，如果缓存中不存在，
     * 则从数据库查询并建立映射关系，然后放入缓存供后续使用。</p>
     *
     * @param platformCode 平台代码
     * @return 源字段到标准字段的映射关系，key为源字段名，value为标准字段对象
     */
    public Map<String, StandardField> getFieldMappingByPlatform(String platformCode) {
        // 添加空值检查
        if (platformCode == null) {
            log.error("平台代码为空，无法获取字段映射关系");
            throw new IllegalArgumentException("platformCode不能为空");
        }

        // 先从缓存中获取
        if (fieldMappingCache.containsKey(platformCode)) {
            log.debug("从缓存中获取平台 {} 的字段映射关系", platformCode);
            return fieldMappingCache.get(platformCode);
        }

        log.debug("为平台 {} 构建字段映射关系", platformCode);

        // 从缓存服务获取平台字段映射关系
        List<PlatformFieldMapping> platformMappings = standardDataCacheService.getPlatformFieldMapping();
        if (platformMappings == null) {
            log.warn("平台字段映射数据为空，平台：{}", platformCode);
            return Collections.emptyMap();
        }

        List<PlatformFieldMapping> filteredMappings = platformMappings.stream().filter(mapping -> platformCode.equals(mapping.getPlatformCode())).toList();

        // 获取所有标准字段并建立code到字段的映射
        Map<String, StandardField> standardFieldMap = standardDataCacheService.getStandardField().stream().collect(Collectors.toMap(StandardField::getFieldCode, field -> field));

        // 建立平台字段到标准字段的映射
        Map<String, StandardField> fieldMapping = filteredMappings.stream().filter(mapping -> standardFieldMap.containsKey(mapping.getStandardFieldCode())).collect(Collectors.toMap(PlatformFieldMapping::getSourceFieldName, mapping -> standardFieldMap.get(mapping.getStandardFieldCode()), (existing, replacement) -> {
            log.warn("发现存在的平台字段映射: '{}', 使用新的映射: {} 替换旧的映射: {}", existing.getFieldNameEn(), replacement.getFieldNameCn(), existing.getFieldNameCn());
            return replacement;
        }));

        // 放入缓存
        fieldMappingCache.put(platformCode, fieldMapping);
        log.debug("平台 {} 的字段映射关系构建完成，共 {} 个映射关系", platformCode, fieldMapping.size());

        return fieldMapping;
    }

    /**
     * 将平台数据映射到标准产品
     *
     * <p>该方法使用字段映射关系将平台数据中的字段值映射到标准产品中，
     * 实现平台数据到标准数据的初步转换。</p>
     *
     * @param productData 平台商品数据
     * @return 初步映射后的标准产品数据
     */
    public DynamicStandardProduct mapFields(ProductDataDTO productData) {
        log.debug("开始字段映射: sourcePlatform={}, platformSkuId={}", productData.getSourcePlatform(), productData.getPlatformSkuId());

        // 获取字段映射关系
        Map<String, StandardField> fieldMapping = getFieldMappingByPlatform(productData.getPlatformCode());
        log.debug("获取到 {} 个字段映射关系", fieldMapping.size());

        // 创建标准产品对象
        DynamicStandardProduct standardProduct = new DynamicStandardProduct();

        // 使用反射获取平台数据对象的所有字段
        Field[] fields = productData.getClass().getDeclaredFields();
        Set<String> mappedFields = new HashSet<>();

        // 先处理有映射关系的字段
        for (Map.Entry<String, StandardField> entry : fieldMapping.entrySet()) {
            String sourceField = entry.getKey();
            StandardField standardField = entry.getValue();

            try {
                // 获取源字段值
                Field sourceFieldObj = findFieldIgnoreCase(sourceField);
                if (sourceFieldObj != null) {
                    sourceFieldObj.setAccessible(true);
                    Object value = sourceFieldObj.get(productData);
                    if (value != null && !value.toString().trim().isEmpty()) {
                        // 使用标准字段名设置值
                        standardProduct.setField(standardField.getFieldNameEn(), value.toString());
                        mappedFields.add(sourceField.toLowerCase()); // 记录时转为小写以忽略大小写
                        log.trace("映射字段: {} -> {} = {}", sourceField, standardField.getFieldNameEn(), value);
                    }
                }
            } catch (Exception e) {
                log.warn("映射字段 {} 时出错: {}", sourceField, e.getMessage(), e);
            }
        }

        // 处理没有映射关系但有值的字段
        for (Field field : fields) {
            try {
                String fieldName = field.getName();
                // 如果该字段已经有映射关系，跳过 (忽略大小写比较)
                if (mappedFields.contains(fieldName.toLowerCase())) {
                    continue;
                }

                // 设置字段可访问
                field.setAccessible(true);

                // 获取字段值
                Object value = field.get(productData);
                if (value != null && !value.toString().trim().isEmpty()) {
                    // 直接使用字段名，字段标准化将在规则引擎中处理
                    standardProduct.setField(fieldName, value.toString());
                    log.trace("直接映射字段: {} = {}", fieldName, value);
                }
            } catch (IllegalAccessException e) {
                log.warn("无法访问字段: {}", field.getName(), e);
            }
        }

        log.debug("字段映射完成，共映射 {} 个字段", standardProduct.getFields().size());
        return standardProduct;
    }

    /**
     * 查找字段（忽略大小写）
     *
     * @param fieldName 字段名
     * @return 字段对象，如果未找到返回null
     */
    private Field findFieldIgnoreCase(String fieldName) {
        // 检查参数是否为null
        if (fieldName == null) {
            return null;
        }

        try {
            // 首先尝试精确匹配
            return ProductDataDTO.class.getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            // 如果精确匹配失败，尝试大小写不敏感匹配
            Field[] fields = ProductDataDTO.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getName().equalsIgnoreCase(fieldName)) {
                    return field;
                }
            }
            return null;
        }
    }
}