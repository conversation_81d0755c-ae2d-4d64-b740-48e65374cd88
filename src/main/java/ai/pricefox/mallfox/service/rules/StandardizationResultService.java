package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.enums.FieldMappingStatusEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.StandardizationResultDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据标准化结果处理服务
 *
 * <p>该服务负责处理数据标准化过程的结果，包括：</p>
 * <ul>
 *   <li>分析标准化后的字段匹配情况</li>
 *   <li>统计匹配、未匹配和空值字段数量</li>
 *   <li>尝试使用原始数据填充未匹配的字段</li>
 *   <li>生成标准化结果报告</li>
 * </ul>
 *
 * <p>主要工作流程：</p>
 * <ol>
 *   <li>获取所有标准字段定义</li>
 *   <li>分析标准化结果中各字段的匹配状态</li>
 *   <li>尝试用原始数据填充未匹配或空值字段</li>
 *   <li>生成包含完整统计信息的标准化结果DTO</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class StandardizationResultService {

    private final StandardDataCacheService standardDataCacheService;
    private final FieldMappingService fieldMappingService;

    /**
     * 处理标准化结果，生成标准化结果DTO
     * <p>
     * 此方法会分析标准化后的数据，并与所有标准字段进行比对，统计匹配、未匹配和空值字段的数量。
     * 同时会尝试用原始数据填充未匹配的字段。
     *
     * @param productData     原始产品数据
     * @param standardProduct 标准化后的产品数据
     * @return 包含标准化数据、字段状态和统计信息的标准化结果DTO
     */
    public StandardizationResultDTO processStandardizationResult(ProductDataDTO productData, DynamicStandardProduct standardProduct) {
        StandardizationResultDTO standardizationResult = new StandardizationResultDTO();

        // 初始化必要的Map字段
        standardizationResult.setStandardizedData(new HashMap<>());
        standardizationResult.setFieldMappingStatus(new HashMap<>());

        // 从缓存获取所有标准字段，并转换为Map格式便于查找
        Map<String, String> allStandardFields = getAllStandardFields();
        standardizationResult.setAllStandardFields(allStandardFields);

        // 分析标准化结果中的字段匹配情况
        analyzeStandardizedFields(standardizationResult, standardProduct, allStandardFields);

        // 对于未匹配或为空的字段，尝试使用原始数据中的值进行填充
        fillUnmatchedFieldsWithOriginalData(standardizationResult, productData);

        // 设置统计信息
        standardizationResult.setTotalFieldsCount(allStandardFields.size());

        return standardizationResult;
    }


    /**
     * 获取所有标准字段
     *
     * @return 以字段英文名为key，中文名为value的Map
     */
    private Map<String, String> getAllStandardFields() {
        List<StandardField> standardFields = standardDataCacheService.getStandardField();
        return standardFields.stream().collect(Collectors.toMap(StandardField::getFieldNameEn, StandardField::getFieldNameCn, (existing, replacement) -> existing)); // 处理重复key的情况
    }

    /**
     * 分析标准化结果中的字段匹配情况
     *
     * @param standardizationResult 标准化结果DTO
     * @param standardProduct       标准化后的产品数据
     * @param allStandardFields     所有标准字段
     */
    private void analyzeStandardizedFields(StandardizationResultDTO standardizationResult, DynamicStandardProduct standardProduct, Map<String, String> allStandardFields) {
        int matchedCount = 0; // 匹配字段数量
        int unmatchedCount = 0; // 未匹配字段数量
        int emptyCount = 0; // 空字段数量
        int matchedTitleCount = 0; // 标题撞库字段数量
        int accessErrorCount = 0; // 数据异常字段数量

        Map<String, Object> standardizedData = standardizationResult.getStandardizedData();
        Map<String, FieldMappingStatusEnum> fieldMappingStatus = standardizationResult.getFieldMappingStatus();

        // 遍历所有标准字段，检查是否在标准化结果中存在
        for (String fieldName : allStandardFields.keySet()) {
            FieldMappingStatusEnum status;
            if (standardProduct.hasFieldWithValue(fieldName)) {
                // 字段存在且有值
                standardizedData.put(fieldName, standardProduct.getStringField(fieldName));
                status = FieldMappingStatusEnum.MATCHED;
                matchedCount++;
            } else if (standardProduct.hasField(fieldName)) {
                // 字段存在但值为空
                standardizedData.put(fieldName, standardProduct.getStringField(fieldName));
                status = FieldMappingStatusEnum.EMPTY;
                emptyCount++;
            } else {
                // 字段不存在
                standardizedData.put(fieldName, null);
                status = FieldMappingStatusEnum.UNMATCHED;
                unmatchedCount++;
            }
            
            fieldMappingStatus.put(fieldName, status);
        }

        // 更新统计信息
        standardizationResult.setMatchedFieldsCount(matchedCount);
        standardizationResult.setUnmatchedFieldsCount(unmatchedCount);
        standardizationResult.setEmptyFieldsCount(emptyCount);
    }

    /**
     * 为未匹配的字段添加原始数据中的值
     *
     * @param standardizationResult 标准化结果DTO
     * @param productData           原始产品数据
     */
    private void fillUnmatchedFieldsWithOriginalData(StandardizationResultDTO standardizationResult, ProductDataDTO productData) {
        Map<String, Object> standardizedData = standardizationResult.getStandardizedData();
        Map<String, FieldMappingStatusEnum> fieldMappingStatus = standardizationResult.getFieldMappingStatus();

        try {

            // 获取平台字段到标准字段的映射关系
            Map<String, StandardField> fieldMapping = new HashMap<>();
            fieldMapping = fieldMappingService.getFieldMappingByPlatform(productData.getPlatformCode());

            // 获取ProductDataDTO的所有字段
            Field[] fields = ProductDataDTO.class.getDeclaredFields();

            for (Field field : fields) {
                processProductDataField(field, productData, fieldMapping, standardizedData, fieldMappingStatus);
            }
        } catch (Exception e) {
            log.error("处理原始数据字段时出错", e);
        }
    }

    /**
     * 处理单个产品数据字段
     *
     * @param field              反射获取的字段
     * @param productData        原始产品数据
     * @param fieldMapping       字段映射关系
     * @param standardizedData   标准化数据
     * @param fieldMappingStatus 字段映射状态
     */
    private void processProductDataField(Field field, ProductDataDTO productData, Map<String, StandardField> fieldMapping, Map<String, Object> standardizedData, Map<String, FieldMappingStatusEnum> fieldMappingStatus) {
        try {
            field.setAccessible(true);
            Object value = field.get(productData);

            // 只处理有值的字段
            if (value != null && !value.toString().trim().isEmpty()) {
                String fieldName = field.getName();

                // 检查是否有对应的标准字段映射
                StandardField standardField = fieldMapping.get(fieldName);
                String standardFieldName = (standardField != null) ? standardField.getFieldNameEn() : null;

                // 如果有映射关系且标准字段未匹配或为空，则使用原始值
                if (standardFieldName != null) {
                    handleMappedField(standardFieldName, value, standardizedData, fieldMappingStatus);
                } else {
                    // 如果没有映射关系但字段在标准化数据中不存在，也尝试添加原始值
                    handleUnmappedField(fieldName, value, standardizedData, fieldMappingStatus);
                }
            }
        } catch (IllegalAccessException e) {
            log.warn("无法访问字段: {}", field.getName(), e);
        }
    }

    /**
     * 处理有映射关系的字段
     *
     * @param standardFieldName  标准字段名
     * @param value              原始值
     * @param standardizedData   标准化数据
     * @param fieldMappingStatus 字段映射状态
     */
    private void handleMappedField(String standardFieldName, Object value, Map<String, Object> standardizedData, Map<String, FieldMappingStatusEnum> fieldMappingStatus) {
        String currentStatus = fieldMappingStatus.get(standardFieldName).name();

        // 如果标准字段未匹配或为空，则使用原始值
        if ("unmatched".equals(currentStatus) || "empty".equals(currentStatus) || !fieldMappingStatus.containsKey(standardFieldName)) {
            standardizedData.put(standardFieldName, value.toString());

            // 更新字段状态
            if (!fieldMappingStatus.containsKey(standardFieldName)) {
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.ORIGINAL_NO_MAPPING);
            } else if ("unmatched".equals(currentStatus)) {
                // 保持unmatched状态，但更新值
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.FILLED_FROM_ORIGINAL);
            }
        }
    }

    /**
     * 处理无映射关系的字段
     *
     * @param fieldName          原始字段名
     * @param value              原始值
     * @param standardizedData   标准化数据
     * @param fieldMappingStatus 字段映射状态
     */
    private void handleUnmappedField(String fieldName, Object value, Map<String, Object> standardizedData, Map<String, FieldMappingStatusEnum> fieldMappingStatus) {
        // 尝试将原始字段名转换为标准字段名格式（驼峰转首字母大写）
        String standardFieldName = toStandardFieldName(fieldName);

        // 如果标准化数据中该字段是unmatched状态，则使用原始值
        if (FieldMappingStatusEnum.UNMATCHED.equals(fieldMappingStatus.get(standardFieldName)) || FieldMappingStatusEnum.EMPTY.equals(fieldMappingStatus.get(standardFieldName)) || !fieldMappingStatus.containsKey(standardFieldName)) {

            standardizedData.put(standardFieldName, value.toString());

            // 更新字段状态
            if (!fieldMappingStatus.containsKey(standardFieldName)) {
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.ORIGINAL_NO_MAPPING);
            } else if (FieldMappingStatusEnum.UNMATCHED.equals(fieldMappingStatus.get(standardFieldName))) {
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.FILLED_FROM_ORIGINAL);
            }
        }
    }

    /**
     * 将驼峰命名转换为标准字段命名（首字母大写）
     * 例如：brand -> Brand, model -> Model
     *
     * @param camelCaseName 驼峰命名
     * @return 标准字段命名
     */
    private String toStandardFieldName(String camelCaseName) {
        if (camelCaseName == null || camelCaseName.isEmpty()) {
            return camelCaseName;
        }
        return Character.toUpperCase(camelCaseName.charAt(0)) + camelCaseName.substring(1);
    }
}
