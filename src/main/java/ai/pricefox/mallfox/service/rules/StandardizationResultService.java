package ai.pricefox.mallfox.service.rules;

import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.enums.FieldMappingStatusEnum;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.StandardizationResultDTO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 数据标准化结果处理服务
 *
 * <p>该服务负责处理数据标准化过程的结果，包括：</p>
 * <ul>
 *   <li>分析标准化后的字段匹配情况</li>
 *   <li>统计匹配、未匹配和空值字段数量</li>
 *   <li>尝试使用原始数据填充未匹配的字段</li>
 *   <li>生成标准化结果报告</li>
 * </ul>
 *
 * <p>主要工作流程：</p>
 * <ol>
 *   <li>获取所有标准字段定义</li>
 *   <li>分析标准化结果中各字段的匹配状态</li>
 *   <li>尝试用原始数据填充未匹配或空值字段</li>
 *   <li>生成包含完整统计信息的标准化结果DTO</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@AllArgsConstructor
public class StandardizationResultService {

    private final StandardDataCacheService standardDataCacheService;
    private final FieldMappingService fieldMappingService;

    /**
     * 处理标准化结果，生成标准化结果DTO
     * <p>
     * 此方法会分析标准化后的数据，并与所有标准字段进行比对，统计匹配、未匹配和空值字段的数量。
     * 同时会尝试用原始数据中的值进行填充。
     *
     * @param productData     原始产品数据
     * @param standardProduct 标准化后的产品数据
     * @return 包含标准化数据、字段状态和统计信息的标准化结果DTO
     */
    public StandardizationResultDTO processStandardizationResult(ProductDataDTO productData, DynamicStandardProduct standardProduct) {
        log.info("[标准化结果服务] 开始处理标准化结果: skuId={}, platform={}", productData.getPlatformSkuId(), productData.getSourcePlatform());

        StandardizationResultDTO standardizationResult = new StandardizationResultDTO();
        log.debug("[标准化结果服务] 创建StandardizationResultDTO对象完成");

        // 初始化必要的Map字段
        standardizationResult.setStandardizedData(new HashMap<>());
        standardizationResult.setFieldMappingStatus(new HashMap<>());
        log.debug("[标准化结果服务] 初始化标准化数据和字段映射状态Map完成");

        // 从缓存获取所有标准字段，并转换为Map格式便于查找
        log.info("[标准化结果服务] 获取所有标准字段");
        Map<String, String> allStandardFields = getAllStandardFields();
        standardizationResult.setAllStandardFields(allStandardFields);
        log.info("[标准化结果服务] 获取到 {} 个标准字段", allStandardFields.size());

        // 分析标准化结果中的字段匹配情况
        log.info("[标准化结果服务] 开始分析标准化字段匹配情况");
        analyzeStandardizedFields(standardizationResult, standardProduct, allStandardFields);
        log.info("[标准化结果服务] 字段匹配情况分析完成");

        // 对于未匹配或为空的字段，尝试使用原始数据中的值进行填充
        log.info("[标准化结果服务] 开始填充未匹配字段");
        fillUnmatchedFieldsWithOriginalData(standardizationResult, productData);
        log.info("[标准化结果服务] 未匹配字段填充完成");

        // 设置统计信息
        standardizationResult.setTotalFieldsCount(allStandardFields.size());
        log.debug("[标准化结果服务] 设置总计字段数: {}", allStandardFields.size());

        log.info("[标准化结果服务] 标准化结果处理完成: matched={}, unmatched={}, empty={}", standardizationResult.getMatchedFieldsCount(), standardizationResult.getUnmatchedFieldsCount(), standardizationResult.getEmptyFieldsCount());

        return standardizationResult;
    }


    /**
     * 获取所有标准字段
     *
     * @return 以字段英文名为key，中文名为value的Map
     */
    private Map<String, String> getAllStandardFields() {
        log.debug("[标准化结果服务] 开始获取所有标准字段");
        List<StandardField> standardFields = standardDataCacheService.getStandardField();
        log.debug("[标准化结果服务] 获取到标准字段 {} 条", standardFields.size());

        Map<String, String> result = standardFields.stream().collect(Collectors.toMap(StandardField::getFieldNameEn, StandardField::getFieldNameCn, (existing, replacement) -> {
            log.warn("[标准化结果服务] 发现重复的标准字段名: {}，使用第一个值", existing);
            return existing;
        })); // 处理重复key的情况

        log.debug("[标准化结果服务] 标准字段Map构建完成，共 {} 个字段", result.size());
        return result;
    }

    /**
     * 分析标准化结果中的字段匹配情况
     *
     * @param standardizationResult 标准化结果DTO
     * @param standardProduct       标准化后的产品数据
     * @param allStandardFields     所有标准字段
     */
    private void analyzeStandardizedFields(StandardizationResultDTO standardizationResult, DynamicStandardProduct standardProduct, Map<String, String> allStandardFields) {
        log.debug("[标准化结果服务] 开始分析标准化字段，标准字段总数: {}", allStandardFields.size());

        int matchedCount = 0; // 匹配字段数量
        int unmatchedCount = 0; // 未匹配字段数量
        int emptyCount = 0; // 空字段数量
        int matchedTitleCount = 0; // 标题撞库字段数量
        int accessErrorCount = 0; // 数据异常字段数量

        Map<String, Object> standardizedData = standardizationResult.getStandardizedData();
        Map<String, FieldMappingStatusEnum> fieldMappingStatus = standardizationResult.getFieldMappingStatus();

        // 遍历所有标准字段，检查是否在标准化结果中存在
        log.debug("[标准化结果服务] 开始遍历所有标准字段");
        for (String fieldName : allStandardFields.keySet()) {
            FieldMappingStatusEnum status;
            if (standardProduct.hasFieldWithValue(fieldName)) {
                // 字段存在且有值
                standardizedData.put(fieldName, standardProduct.getStringField(fieldName));
                status = FieldMappingStatusEnum.MATCHED;
                matchedCount++;
                log.trace("[标准化结果服务] 字段匹配成功: {} = {}", fieldName, standardProduct.getStringField(fieldName));
            } else if (standardProduct.hasField(fieldName)) {
                // 字段存在但值为空
                standardizedData.put(fieldName, standardProduct.getStringField(fieldName));
                status = FieldMappingStatusEnum.EMPTY;
                emptyCount++;
                log.trace("[标准化结果服务] 字段存在但值为空: {}", fieldName);
            } else {
                // 字段不存在
                standardizedData.put(fieldName, null);
                status = FieldMappingStatusEnum.UNMATCHED;
                unmatchedCount++;
                log.trace("[标准化结果服务] 字段未匹配: {}", fieldName);
            }

            fieldMappingStatus.put(fieldName, status);
        }
        log.debug("[标准化结果服务] 字段分析完成: matched={}, unmatched={}, empty={}", matchedCount, unmatchedCount, emptyCount);

        // 更新统计信息
        standardizationResult.setMatchedFieldsCount(matchedCount);
        standardizationResult.setUnmatchedFieldsCount(unmatchedCount);
        standardizationResult.setEmptyFieldsCount(emptyCount);
        log.debug("[标准化结果服务] 统计信息更新完成");
    }

    /**
     * 为未匹配的字段添加原始数据中的值
     *
     * @param standardizationResult 标准化结果DTO
     * @param productData           原始产品数据
     */
    private void fillUnmatchedFieldsWithOriginalData(StandardizationResultDTO standardizationResult, ProductDataDTO productData) {
        log.debug("[标准化结果服务] 开始填充未匹配字段");
        Map<String, Object> standardizedData = standardizationResult.getStandardizedData();
        Map<String, FieldMappingStatusEnum> fieldMappingStatus = standardizationResult.getFieldMappingStatus();

        try {
            log.debug("[标准化结果服务] 获取平台字段到标准字段的映射关系");
            // 获取平台字段到标准字段的映射关系
            Map<String, StandardField> fieldMapping = new HashMap<>();
            fieldMapping = fieldMappingService.getFieldMappingByPlatform(productData.getPlatformCode());
            log.debug("[标准化结果服务] 获取到字段映射关系 {} 条", fieldMapping.size());

            // 获取ProductDataDTO的所有字段
            log.debug("[标准化结果服务] 获取ProductDataDTO的所有字段");
            Field[] fields = ProductDataDTO.class.getDeclaredFields();
            log.debug("[标准化结果服务] 获取到ProductDataDTO字段 {} 个", fields.length);

            int filledCount = 0;
            log.debug("[标准化结果服务] 开始处理每个产品数据字段");
            for (Field field : fields) {
                processProductDataField(field, productData, fieldMapping, standardizedData, fieldMappingStatus);
                filledCount++;
            }
            log.debug("[标准化结果服务] 处理完成，共处理 {} 个字段", filledCount);
        } catch (Exception e) {
            log.error("[标准化结果服务] 处理原始数据字段时出错", e);
        }

        log.debug("[标准化结果服务] 未匹配字段填充完成");
    }

    /**
     * 处理单个产品数据字段
     *
     * @param field              反射获取的字段
     * @param productData        原始产品数据
     * @param fieldMapping       字段映射关系
     * @param standardizedData   标准化数据
     * @param fieldMappingStatus 字段映射状态
     */
    private void processProductDataField(Field field, ProductDataDTO productData, Map<String, StandardField> fieldMapping, Map<String, Object> standardizedData, Map<String, FieldMappingStatusEnum> fieldMappingStatus) {
        try {
            field.setAccessible(true);
            Object value = field.get(productData);

            // 只处理有值的字段
            if (value != null && !value.toString().trim().isEmpty()) {
                String fieldName = field.getName();

                // 检查是否有对应的标准字段映射
                StandardField standardField = fieldMapping.get(fieldName);
                String standardFieldName = (standardField != null) ? standardField.getFieldNameEn() : null;

                // 如果有映射关系且标准字段未匹配或为空，则使用原始值
                if (standardFieldName != null) {
                    handleMappedField(standardFieldName, value, standardizedData, fieldMappingStatus);
                } else {
                    // 如果没有映射关系但字段在标准化数据中不存在，也尝试添加原始值
                    handleUnmappedField(fieldName, value, standardizedData, fieldMappingStatus);
                }
            } else {
                log.trace("[标准化结果服务] 字段值为空，跳过处理: {}", field.getName());
            }
        } catch (IllegalAccessException e) {
            log.warn("[标准化结果服务] 无法访问字段: {}", field.getName(), e);
        }
    }

    /**
     * 处理有映射关系的字段
     *
     * @param standardFieldName  标准字段名
     * @param value              原始值
     * @param standardizedData   标准化数据
     * @param fieldMappingStatus 字段映射状态
     */
    private void handleMappedField(String standardFieldName, Object value, Map<String, Object> standardizedData, Map<String, FieldMappingStatusEnum> fieldMappingStatus) {
        String currentStatus = fieldMappingStatus.get(standardFieldName) != null ? fieldMappingStatus.get(standardFieldName).name() : "unknown";

        log.trace("[标准化结果服务] 处理有映射关系的字段: {} (当前状态: {})", standardFieldName, currentStatus);

        // 如果标准字段未匹配或为空，则使用原始值
        if ("unmatched".equals(currentStatus) || "empty".equals(currentStatus) || !fieldMappingStatus.containsKey(standardFieldName)) {
            standardizedData.put(standardFieldName, value.toString());
            log.trace("[标准化结果服务] 使用原始值填充映射字段: {} = {}", standardFieldName, value);

            // 更新字段状态
            if (!fieldMappingStatus.containsKey(standardFieldName)) {
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.ORIGINAL_NO_MAPPING);
                log.trace("[标准化结果服务] 设置字段状态为ORIGINAL_NO_MAPPING: {}", standardFieldName);
            } else if ("unmatched".equals(currentStatus)) {
                // 保持unmatched状态，但更新值
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.FILLED_FROM_ORIGINAL);
                log.trace("[标准化结果服务] 设置字段状态为FILLED_FROM_ORIGINAL: {}", standardFieldName);
            }
        } else {
            log.trace("[标准化结果服务] 字段 {} 已有有效值，无需填充", standardFieldName);
        }
    }

    /**
     * 处理无映射关系的字段
     *
     * @param fieldName          原始字段名
     * @param value              原始值
     * @param standardizedData   标准化数据
     * @param fieldMappingStatus 字段映射状态
     */
    private void handleUnmappedField(String fieldName, Object value, Map<String, Object> standardizedData, Map<String, FieldMappingStatusEnum> fieldMappingStatus) {
        // 尝试将原始字段名转换为标准字段名格式（驼峰转首字母大写）
        String standardFieldName = toStandardFieldName(fieldName);
        log.trace("[标准化结果服务] 处理无映射关系的字段: {} -> {}", fieldName, standardFieldName);

        // 如果标准化数据中该字段是unmatched状态，则使用原始值
        FieldMappingStatusEnum currentStatus = fieldMappingStatus.get(standardFieldName);
        if (FieldMappingStatusEnum.UNMATCHED.equals(currentStatus) || FieldMappingStatusEnum.EMPTY.equals(currentStatus) || !fieldMappingStatus.containsKey(standardFieldName)) {

            standardizedData.put(standardFieldName, value.toString());
            log.trace("[标准化结果服务] 使用原始值填充无映射字段: {} = {}", standardFieldName, value);

            // 更新字段状态
            if (!fieldMappingStatus.containsKey(standardFieldName)) {
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.ORIGINAL_NO_MAPPING);
                log.trace("[标准化结果服务] 设置字段状态为ORIGINAL_NO_MAPPING: {}", standardFieldName);
            } else if (FieldMappingStatusEnum.UNMATCHED.equals(currentStatus)) {
                fieldMappingStatus.put(standardFieldName, FieldMappingStatusEnum.FILLED_FROM_ORIGINAL);
                log.trace("[标准化结果服务] 设置字段状态为FILLED_FROM_ORIGINAL: {}", standardFieldName);
            }
        } else {
            log.trace("[标准化结果服务] 字段 {} 已有有效值，无需填充", standardFieldName);
        }
    }

    /**
     * 将驼峰命名转换为标准字段命名（首字母大写）
     * 例如：brand -> Brand, model -> Model
     *
     * @param camelCaseName 驼峰命名
     * @return 标准字段命名
     */
    private String toStandardFieldName(String camelCaseName) {
        if (camelCaseName == null || camelCaseName.isEmpty()) {
            return camelCaseName;
        }
        return Character.toUpperCase(camelCaseName.charAt(0)) + camelCaseName.substring(1);
    }

    /**
     * 处理标准化结果，生成标准化结果DTO
     * <p>
     * 此方法会分析标准化后的数据，并与所有标准字段进行比对，统计匹配、未匹配和空值字段的数量。
     * 同时会尝试用原始数据中的值进行填充。
     *
     * @param productDataMap  原始产品数据 (JSON格式)
     * @param standardProduct 标准化后的产品数据
     * @return 包含标准化数据、字段状态和统计信息的标准化结果DTO
     */
    public StandardizationResultDTO processStandardizationResultFromJson(Map<String, Object> productDataMap, DynamicStandardProduct standardProduct) {
        log.info("[标准化结果服务] 开始处理标准化结果: skuId={}", productDataMap.get("platformSkuId"));

        StandardizationResultDTO standardizationResult = new StandardizationResultDTO();
        log.debug("[标准化结果服务] 创建StandardizationResultDTO对象完成");

        // 初始化必要的Map字段
        standardizationResult.setStandardizedData(new HashMap<>());
        standardizationResult.setFieldMappingStatus(new HashMap<>());
        log.debug("[标准化结果服务] 初始化标准化数据和字段映射状态Map完成");

        // 从缓存获取所有标准字段，并转换为Map格式便于查找
        log.info("[标准化结果服务] 获取所有标准字段");
        Map<String, String> allStandardFields = getAllStandardFields();
        standardizationResult.setAllStandardFields(allStandardFields);
        log.info("[标准化结果服务] 获取到 {} 个标准字段", allStandardFields.size());

        // 分析标准化结果中的字段匹配情况
        log.info("[标准化结果服务] 开始分析标准化字段匹配情况");
        analyzeStandardizedFields(standardizationResult, standardProduct, allStandardFields);
        log.info("[标准化结果服务] 字段匹配情况分析完成");

        // 对于未匹配或为空的字段，尝试使用原始数据中的值进行填充
        log.info("[标准化结果服务] 开始填充未匹配字段");
        fillUnmatchedFieldsWithOriginalDataFromJson(standardizationResult, productDataMap);
        log.info("[标准化结果服务] 未匹配字段填充完成");

        // 设置统计信息
        standardizationResult.setTotalFieldsCount(allStandardFields.size());
        log.debug("[标准化结果服务] 设置总计字段数: {}", allStandardFields.size());

        log.info("[标准化结果服务] 标准化结果处理完成: matched={}, unmatched={}, empty={}", standardizationResult.getMatchedFieldsCount(), standardizationResult.getUnmatchedFieldsCount(), standardizationResult.getEmptyFieldsCount());

        return standardizationResult;
    }

    /**
     * 为未匹配的字段添加原始数据中的值
     *
     * @param standardizationResult 标准化结果DTO
     * @param productDataMap        原始产品数据 (JSON格式)
     */
    private void fillUnmatchedFieldsWithOriginalDataFromJson(StandardizationResultDTO standardizationResult, Map<String, Object> productDataMap) {
        log.debug("[标准化结果服务] 开始填充未匹配字段");
        Map<String, Object> standardizedData = standardizationResult.getStandardizedData();
        Map<String, FieldMappingStatusEnum> fieldMappingStatus = standardizationResult.getFieldMappingStatus();

        try {
            log.debug("[标准化结果服务] 获取平台字段到标准字段的映射关系");
            // 获取平台字段到标准字段的映射关系
            Map<String, StandardField> fieldMapping = new HashMap<>();
            String platformCode = (String) productDataMap.get("platformCode");
            if (platformCode != null) {
                fieldMapping = fieldMappingService.getFieldMappingByPlatform(platformCode);
                log.debug("[标准化结果服务] 获取到字段映射关系 {} 条", fieldMapping.size());
            } else {
                log.warn("[标准化结果服务] 平台代码为空，无法获取字段映射关系");
            }

            int filledCount = 0;
            log.debug("[标准化结果服务] 开始处理每个产品数据字段");
            for (Map.Entry<String, Object> entry : productDataMap.entrySet()) {
                String fieldName = entry.getKey();
                Object value = entry.getValue();

                // 只处理有值的字段
                if (value != null && !value.toString().trim().isEmpty()) {
                    // 检查是否有对应的标准字段映射
                    StandardField standardField = fieldMapping.get(fieldName);
                    String standardFieldName = (standardField != null) ? standardField.getFieldNameEn() : null;

                    // 如果有映射关系且标准字段未匹配或为空，则使用原始值
                    if (standardFieldName != null) {
                        handleMappedField(standardFieldName, value, standardizedData, fieldMappingStatus);
                    } else {
                        // 如果没有映射关系但字段在标准化数据中不存在，也尝试添加原始值
                        handleUnmappedField(fieldName, value, standardizedData, fieldMappingStatus);
                    }
                    filledCount++;
                } else {
                    log.trace("[标准化结果服务] 字段值为空，跳过处理: {}", fieldName);
                }
            }
            log.debug("[标准化结果服务] 处理完成，共处理 {} 个字段", filledCount);
        } catch (Exception e) {
            log.error("[标准化结果服务] 处理原始数据字段时出错", e);
        }

        log.debug("[标准化结果服务] 未匹配字段填充完成");
    }
}