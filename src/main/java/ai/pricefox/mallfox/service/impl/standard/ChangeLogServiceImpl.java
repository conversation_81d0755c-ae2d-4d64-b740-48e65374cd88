package ai.pricefox.mallfox.service.impl.standard;

import ai.pricefox.mallfox.domain.standard.ChangeLog;
import ai.pricefox.mallfox.mapper.standard.ChangeLogMapper;
import ai.pricefox.mallfox.service.standard.ChangeLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 变更日志Service实现
 */
@Service
public class ChangeLogServiceImpl extends ServiceImpl<ChangeLogMapper, ChangeLog> implements ChangeLogService {

    @Override
    public void recordRawDataChange(Object productData, String changeType, String operator) {
        ChangeLog changeLog = new ChangeLog();
        changeLog.setChangeType("RAW_DATA_" + changeType);
        changeLog.setChangeDescription("原始数据变更: " + changeType);
        changeLog.setOperatorName(operator);
        save(changeLog);
    }

    @Override
    public void recordDataMergeChange(String productIdentifier, String platform, String changeType, String operator) {
        ChangeLog changeLog = new ChangeLog();
        changeLog.setProductIdentifier(productIdentifier);
        changeLog.setChangeType("DATA_MERGE_" + changeType);
        changeLog.setChangeDescription("数据合并变更: " + platform);
        changeLog.setOperatorName(operator);
        save(changeLog);
    }

    @Override
    public void recordManualFieldChange(String productIdentifier, String fieldName, String originalValue, String newValue, String operator) {
        ChangeLog changeLog = new ChangeLog();
        changeLog.setProductIdentifier(productIdentifier);
        changeLog.setChangeType("MANUAL_FIELD_CHANGE");
        changeLog.setChangeDescription(String.format("人工修改字段 [%s]: 从 '%s' 改为 '%s'", fieldName, originalValue, newValue));
        changeLog.setOperatorName(operator);
        save(changeLog);
    }
}