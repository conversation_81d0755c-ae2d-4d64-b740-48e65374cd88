package ai.pricefox.mallfox.service.impl.standard;

import ai.pricefox.mallfox.domain.standard.StandardizationStepTracking;
import ai.pricefox.mallfox.mapper.standard.StandardizationStepTrackingMapper;
import ai.pricefox.mallfox.service.standard.StandardizationStepTrackingService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 标准化步骤跟踪Service实现
 */
@Service
public class StandardizationStepTrackingServiceImpl extends ServiceImpl<StandardizationStepTrackingMapper, StandardizationStepTracking> implements StandardizationStepTrackingService {

    @Override
    public StandardizationStepTracking getByProductIdentifier(String productIdentifier) {
        return query().eq("product_identifier", productIdentifier).one();
    }
}