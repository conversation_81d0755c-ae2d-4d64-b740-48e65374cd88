//package ai.pricefox.mallfox.service.impl;
//
//import ai.pricefox.mallfox.model.response.ExpertReviewResponse;
//import ai.pricefox.mallfox.model.response.ProductPageResponse;
//import ai.pricefox.mallfox.vo.base.PageResult;
//import org.springframework.stereotype.Service;
//import org.springframework.util.StringUtils;
//
//import java.text.DecimalFormat;
//import java.time.LocalDateTime;
//import java.time.format.DateTimeFormatter;
//import java.util.*;
//import java.util.concurrent.ThreadLocalRandom;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @desc 商品mock服务
// * @since 2025/6/28
// */
//@Service
//public class MockProductService {
//
//    private static final List<ProductPageResponse> MOCK_PRODUCT_DATABASE = new ArrayList<>();
//    private static final List<ExpertReviewResponse> MOCK_EXPERT_REVIEW_DATABASE = new ArrayList<>();
//
//    // 在类加载时，只生成一次模拟数据
//    static {
//        generateMockData();
//        generateMockExpertReviewData();
//    }
//
//    /**
//     * 获取模拟商品的分页列表
//     *
//     * @param query    搜索查询词 (对应UI上的标签或品牌)
//     * @param pageNo   页码
//     * @param pageSize 每页数量
//     * @return 分页后的商品数据
//     */
//    public PageResult<ProductPageResponse> getMockProductPage(String query, int pageNo, int pageSize) {
//        List<ProductPageResponse> processedList = new ArrayList<>(MOCK_PRODUCT_DATABASE);
//
//        if (StringUtils.hasText(query)) {
//            String lowerCaseQuery = query.toLowerCase();
//
//            // 处理排序/标签类查询
//            switch (lowerCaseQuery) {
//                case "best-seller":
//                case "top-rated":
//                    // 按评分降序排序
//                    processedList.sort(Comparator.comparing(ProductPageResponse::getRating).reversed());
//                    break;
//                case "trending":
//                    // 模拟"热门"，简单地打乱顺序
//                    Collections.shuffle(processedList);
//                    break;
//                case "new-releases":
//                    // 模拟"新品"，将列表倒序 (假设ID是按顺序生成的)
//                    Collections.reverse(processedList);
//                    break;
//                default:
//                    // 默认处理为品牌或标题筛选
//                    processedList = processedList.stream()
//                            .filter(p -> p.getTitle().toLowerCase().contains(lowerCaseQuery))
//                            .collect(Collectors.toList());
//                    break;
//            }
//        }
//
//        // 3. 手动实现分页
//        long total = processedList.size();
//        int startIndex = (pageNo - 1) * pageSize;
//
//        // 如果起始索引超出总数，返回空页
//        if (startIndex >= total) {
//            return PageResult.empty();
//        }
//
//        int endIndex = Math.min(startIndex + pageSize, (int) total);
//        List<ProductPageResponse> pageContent = processedList.subList(startIndex, endIndex);
//
//        // 4. 组装并返回分页响应对象
//        return new PageResult<>(pageContent, total);
//    }
//
//    /**
//     * 获取模拟专家评论的分页列表
//     *
//     * @param query    搜索查询词
//     * @param pageNo   页码
//     * @param pageSize 每页数量
//     * @return 分页后的专家评论数据
//     */
//    public PageResult<ExpertReviewResponse> getMockExpertPage(String query, int pageNo, int pageSize) {
//        List<ExpertReviewResponse> processedList = new ArrayList<>(MOCK_EXPERT_REVIEW_DATABASE);
//
//        // 根据查询条件筛选和排序
//        if (StringUtils.hasText(query)) {
//            String lowerCaseQuery = query.toLowerCase();
//
//            // 处理排序/标签类查询
//            switch (lowerCaseQuery) {
//                case "latest":
//                    // 按发布时间排序（最新的在前）
//                    Collections.reverse(processedList);
//                    break;
//                case "popular":
//                    // 按评分降序排序
//                    processedList.sort(Comparator.comparing(ExpertReviewResponse::getRating,
//                        Comparator.nullsLast(Comparator.reverseOrder())));
//                    break;
//                case "recommended":
//                    // 只显示推荐的评论
//                    processedList = processedList.stream()
//                            .filter(r -> Boolean.TRUE.equals(r.getRecommended()))
//                            .collect(Collectors.toList());
//                    break;
//                default:
//                    // 默认处理为品牌或标题筛选
//                    processedList = processedList.stream()
//                            .filter(r -> r.getTitle().toLowerCase().contains(lowerCaseQuery) ||
//                                       r.getBrand().toLowerCase().contains(lowerCaseQuery) ||
//                                       r.getModel().toLowerCase().contains(lowerCaseQuery))
//                            .collect(Collectors.toList());
//                    break;
//            }
//        }
//
//        // 手动实现分页
//        long total = processedList.size();
//        int startIndex = (pageNo - 1) * pageSize;
//
//        // 如果起始索引超出总数，返回空页
//        if (startIndex >= total) {
//            return PageResult.empty();
//        }
//
//        int endIndex = Math.min(startIndex + pageSize, (int) total);
//        List<ExpertReviewResponse> pageContent = processedList.subList(startIndex, endIndex);
//
//        // 组装并返回分页响应对象
//        return new PageResult<>(pageContent, total);
//    }
//
//    /**
//     * 生成模拟数据的核心方法
//     */
//    private static void generateMockData() {
//        // === 步骤 1: 将模型和图片URL建立直接映射关系 ===
//        // 这样可以确保每个模型都使用正确的图片。
//        final Map<String, String> modelImageMap = Map.ofEntries(
//                Map.entry("iPhone 15 Pro", "https://m.media-amazon.com/images/I/51-dI0OmzyL._AC_SL1000_.jpg"),
//                Map.entry("iPhone 15", "https://m.media-amazon.com/images/I/71-9RmL+4+L._AC_SL1114_.jpg"),
//                Map.entry("iPhone SE", "https://m.media-amazon.com/images/I/61rDLIISfiL._AC_SL1000_.jpg"),
//                Map.entry("Galaxy S24 Ultra", "https://m.media-amazon.com/images/I/71NngboUC6L._AC_SL1500_.jpg"),
//                Map.entry("Galaxy Z Fold 5", "https://m.media-amazon.com/images/I/61hdrl3iMXL._AC_SL1500_.jpg"),
//                Map.entry("Galaxy A55", "https://www.wilsoncomm.com.hk/image/cache/catalog/product-1805/2569041d429088b9e067dcdec12c4259-850x850.jpg"),
//                Map.entry("14 Ultra", "https://kddi-h.assetsadobe3.com/is/image/content/dam/au-com/mobile/product/plus-one/xiaomi-14-ultra/images/xiaomi-14-ultra_img_02.jpg?scl=1&qlt=90"),
//                Map.entry("Redmi Note 13 Pro", "https://shop.uqmobile.jp/system/images/device_patterns/redmi_note13_pro_5g/img_redmi_note13-pro_perple02.webp")
////                Map.entry("Poco F6", "https://m.media-amazon.com/images/I/31saGE7xiVL._AC_SL1000_.jpg")
//        );
//        // 备用图片，防止某个型号在Map中找不到图片
//        final String fallbackImageUrl = "https://m.media-amazon.com/images/I/61vK-9zSurL._AC_SL1500_.jpg";
//
//        String[] brands = {"Apple", "Samsung", "Xiaomi"};
//        String[] appleModels = {"iPhone 15 Pro", "iPhone 15", "iPhone SE"};
//        String[] samsungModels = {"Galaxy S24 Ultra", "Galaxy Z Fold 5", "Galaxy A55"};
//        String[] xiaomiModels = {"14 Ultra", "Redmi Note 13 Pro"};
////        String[] colors = {"Titanium Black", "Starlight", "Deep Purple", "Sky Blue", "Phantom White"};
//        String[] storages = {"128GB", "256GB", "512GB", "1TB"};
//
//        DecimalFormat df = new DecimalFormat("0.00");
//        DecimalFormat ratingDf = new DecimalFormat("0.0");
//
//        for (int i = 0; i < 150; i++) {
//            String brand = brands[i % brands.length];
//            String model;
//            switch (brand) {
//                case "Samsung":
//                    model = samsungModels[i % samsungModels.length];
//                    break;
//                case "Xiaomi":
//                    model = xiaomiModels[i % xiaomiModels.length];
//                    break;
//                default: // Apple
//                    model = appleModels[i % appleModels.length];
//                    break;
//            }
//
////            String color = colors[i % colors.length];
//            String storage = storages[i % storages.length];
//            String title = brand + " " + model + " " + storage ; //+ " - " + color;
//
//            String imageUrl = modelImageMap.getOrDefault(model, fallbackImageUrl);
//
//            double listPrice = ThreadLocalRandom.current().nextDouble(499.99, 1599.99);
//            double discountPercentage = ThreadLocalRandom.current().nextInt(5, 25);
//            double price = listPrice * (1 - discountPercentage / 100);
//
//            String discountLabel = "-" + (int) discountPercentage + "%";
//            String installmentInfo = "Or $" + df.format(price / 12) + "/mo.(x12)*";
//            double rating = ThreadLocalRandom.current().nextDouble(3.8, 5.0);
//
//            MOCK_PRODUCT_DATABASE.add(new ProductPageResponse(
//                    i + 1,
//                    title,
//                    imageUrl, // <-- 使用从Map中获取的、正确的图片URL
//                    Double.parseDouble(df.format(price)),
//                    Double.parseDouble(df.format(listPrice)),
//                    discountLabel,
//                    ThreadLocalRandom.current().nextBoolean(),
//                    Double.parseDouble(ratingDf.format(rating)),
//                    installmentInfo
//            ));
//        }
//    }
//
//    /**
//     * 生成专家评论模拟数据
//     */
//    private static void generateMockExpertReviewData() {
//        // 专家评论的图片映射
//        final Map<String, String> reviewImageMap = Map.ofEntries(
//                Map.entry("iPhone 15 Pro Max", "https://m.media-amazon.com/images/I/51-dI0OmzyL._AC_SL1000_.jpg"),
//                Map.entry("iPhone 15", "https://m.media-amazon.com/images/I/71-9RmL+4+L._AC_SL1114_.jpg"),
//                Map.entry("iPhone SE", "https://m.media-amazon.com/images/I/61rDLIISfiL._AC_SL1000_.jpg"),
//                Map.entry("Galaxy S24 Ultra", "https://m.media-amazon.com/images/I/71NngboUC6L._AC_SL1500_.jpg"),
//                Map.entry("Galaxy Z Fold 5", "https://m.media-amazon.com/images/I/61hdrl3iMXL._AC_SL1500_.jpg"),
//                Map.entry("Galaxy A55", "https://www.wilsoncomm.com.hk/image/cache/catalog/product-1805/2569041d429088b9e067dcdec12c4259-850x850.jpg"),
//                Map.entry("Xiaomi 14 Ultra", "https://kddi-h.assetsadobe3.com/is/image/content/dam/au-com/mobile/product/plus-one/xiaomi-14-ultra/images/xiaomi-14-ultra_img_02.jpg?scl=1&qlt=90"),
//                Map.entry("Redmi Note 13 Pro", "https://shop.uqmobile.jp/system/images/device_patterns/redmi_note13_pro_5g/img_redmi_note13-pro_perple02.webp")
//        );
//
//        String[] brands = {"Apple", "Samsung", "Xiaomi"};
//        String[] appleModels = {"iPhone 15 Pro Max", "iPhone 15", "iPhone SE"};
//        String[] samsungModels = {"Galaxy S24 Ultra", "Galaxy Z Fold 5", "Galaxy A55"};
//        String[] xiaomiModels = {"Xiaomi 14 Ultra", "Redmi Note 13 Pro"};
//
//        String[] reviewTypes = {"In-Depth Review", "Quick Review", "Comparison", "First Impressions", "Long-term Review"};
//        String[] reviewers = {"Techreviewer", "TechExpert", "MobileGuru", "GadgetPro", "PhoneSpecialist"};
//        String[] readTimes = {"5min read", "8min read", "12min read", "15min read", "20min read"};
//
//        // 评论标题模板
//        String[] titleTemplates = {
//            "%s %s In-Depth Review: %s",
//            "%s %s Review: Is it worth the upgrade?",
//            "%s %s: The complete buyer's guide",
//            "%s %s vs Competition: Which should you buy?",
//            "%s %s Long-term Review: After 3 months of use"
//        };
//
//        String[] reviewAspects = {"Photography", "Performance", "Battery Life", "Design", "Value for Money", "Camera Quality", "Display", "Gaming"};
//
//        // 描述模板
//        String[] descriptionTemplates = {
//            "After two weeks of intensive use, let's see if %s latest flagship is worth...",
//            "We put the %s %s through its paces to find out if it lives up to the hype...",
//            "Is the %s %s the best smartphone you can buy right now? Our comprehensive review...",
//            "From camera quality to battery life, we test everything about the %s %s...",
//            "Three months with the %s %s: Here's what we learned about this flagship device..."
//        };
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMM dd, yyyy");
//
//        for (int i = 0; i < 50; i++) {
//            String brand = brands[i % brands.length];
//            String model;
//            switch (brand) {
//                case "Samsung":
//                    model = samsungModels[i % samsungModels.length];
//                    break;
//                case "Xiaomi":
//                    model = xiaomiModels[i % xiaomiModels.length];
//                    break;
//                default: // Apple
//                    model = appleModels[i % appleModels.length];
//                    break;
//            }
//
//            String reviewType = reviewTypes[i % reviewTypes.length];
//            String reviewer = reviewers[i % reviewers.length];
//            String readTime = readTimes[i % readTimes.length];
//            String aspect = reviewAspects[i % reviewAspects.length];
//
//            String title = String.format(titleTemplates[i % titleTemplates.length], brand, model, aspect);
//            String description = String.format(descriptionTemplates[i % descriptionTemplates.length], brand, model);
//
//            String imageUrl = reviewImageMap.getOrDefault(model, "https://m.media-amazon.com/images/I/61vK-9zSurL._AC_SL1500_.jpg");
//
//            // 生成发布时间（最近3个月内的随机时间）
//            LocalDateTime publishDate = LocalDateTime.now().minusDays(ThreadLocalRandom.current().nextInt(1, 90));
//            String publishTime = publishDate.format(formatter);
//
//            Double rating = ThreadLocalRandom.current().nextDouble(3.5, 5.0);
//            rating = Math.round(rating * 10.0) / 10.0;
//
//            Boolean recommended = rating >= 4.0;
//
//            MOCK_EXPERT_REVIEW_DATABASE.add(new ExpertReviewResponse(
//                    i + 1,
//                    title,
//                    imageUrl,
//                    description,
//                    reviewer,
//                    readTime,
//                    publishTime,
//                    brand,
//                    model,
//                    reviewType,
//                    rating,
//                    recommended
//            ));
//        }
//    }
//}
