package ai.pricefox.mallfox.service.impl.standard;

import ai.pricefox.mallfox.domain.standard.StandardizationOperationLog;
import ai.pricefox.mallfox.mapper.standard.StandardizationOperationLogMapper;
import ai.pricefox.mallfox.service.standard.StandardizationOperationLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 标准化操作日志Service实现
 */
@Service
public class StandardizationOperationLogServiceImpl 
    extends ServiceImpl<StandardizationOperationLogMapper, StandardizationOperationLog> 
    implements StandardizationOperationLogService {

    @Override
    public void recordOperation(StandardizationOperationLog operationLog) {
        save(operationLog);
    }
}