package ai.pricefox.mallfox.service.impl.standard;

import ai.pricefox.mallfox.domain.standard.StandardizationIntermediateData;
import ai.pricefox.mallfox.mapper.standard.StandardizationIntermediateDataMapper;
import ai.pricefox.mallfox.service.standard.StandardizationIntermediateDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 标准化中间数据Service实现
 */
@Service
public class StandardizationIntermediateDataServiceImpl 
    extends ServiceImpl<StandardizationIntermediateDataMapper, StandardizationIntermediateData> 
    implements StandardizationIntermediateDataService {

    @Override
    public StandardizationIntermediateData getByProductIdentifier(String productIdentifier) {
        return query().eq("product_identifier", productIdentifier).one();
    }

    @Override
    public void saveOrUpdateIntermediateData(StandardizationIntermediateData data) {
        saveOrUpdate(data);
    }
}