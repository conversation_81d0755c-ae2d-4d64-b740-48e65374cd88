//package ai.pricefox.mallfox.service.impl;
//
//import ai.pricefox.mallfox.common.exception.util.BizException;
//import ai.pricefox.mallfox.common.RedisUtils;
//import ai.pricefox.mallfox.common.exception.enums.ResultCode;
//import ai.pricefox.mallfox.common.constant.RedisKeys;
//import ai.pricefox.mallfox.domain.DataCalibrationTags;
//import ai.pricefox.mallfox.mapper.ProductViewMapper;
//import ai.pricefox.mallfox.model.param.CalibrationTagRequest;
//import ai.pricefox.mallfox.model.param.ProductDataSearchRequest;
//import ai.pricefox.mallfox.model.response.ProductDataViewResponse;
//import ai.pricefox.mallfox.model.response.SkuMasterViewDTO;
//import ai.pricefox.mallfox.model.response.SpuGroupViewResponse;
//import ai.pricefox.mallfox.service.integration.impl.CalibrationService;
//import com.alibaba.fastjson.JSON;
//import com.baomidou.mybatisplus.core.metadata.IPage;
//import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
//import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.*;
//import java.util.concurrent.TimeUnit;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @desc 商品数据展示视图
// * @since 2025/6/24
// */
//@Service
//@Slf4j
//public class ProductViewGroupedService {
//
//    @Autowired
//    private ProductViewMapper productViewMapper;
//
//    @Autowired
//    private RedisUtils redisUtils;
//
//    @Autowired
//    private CalibrationService calibrationService;
//
//    public IPage<SpuGroupViewResponse> getSpuGroupedView(ProductDataSearchRequest request) {
//        // 一级：分页查询SPU列表
//        Page<ProductDataSearchRequest> page = new Page<>(request.getPageNo(), request.getPageSize());
//        IPage<SpuGroupViewResponse> spuPage = productViewMapper.selectSpuList(page, request);
//        List<SpuGroupViewResponse> spuList = spuPage.getRecords();
//
//        if (CollectionUtils.isEmpty(spuList)) {
//            return new Page<>(request.getPageNo(), request.getPageSize());
//        }
//
//        // 为SPU数据附加校准标记
//        attachCalibrationTagsForSpu(spuList);
//
//        // 为每个 SPU 设置一级树形结构的 id 和 pid
//        for (SpuGroupViewResponse spuGroup : spuList) {
//            spuGroup.setId(spuGroup.getSpuId());
//            spuGroup.setPid("0");
//        }
//        return spuPage;
//    }
//
//    /**
//     * 二级：根据SPU ID获取SKU列表
//     *
//     * @param request 包含spuId和分页参数的请求
//     * @return SKU列表分页结果
//     */
//    public IPage<SpuGroupViewResponse> getSkuListBySpuId(ProductDataSearchRequest request) {
//        // 使用分页查询该SPU下的SKU
//        String spuId = request.getSpuId();
//        Page<ProductDataSearchRequest> page = new Page<>(request.getPageNo(), request.getPageSize());
//        IPage<SkuMasterViewDTO> skuPage = productViewMapper.selectSkuListBySpuIdWithPage(page, spuId);
//
//        List<SkuMasterViewDTO> skuList = skuPage.getRecords();
//        if (CollectionUtils.isEmpty(skuList)) {
//            return new Page<>(request.getPageNo(), request.getPageSize());
//        }
//
//        // 获取并附加校准标记数据
//        attachCalibrationTags(skuList);
//
//        // 转换为SpuGroupViewResponse，设置二级结构
//        List<SpuGroupViewResponse> skuResponses = skuList.stream()
//                .map(sku -> {
//                    SpuGroupViewResponse response = new SpuGroupViewResponse();
//                    BeanUtils.copyProperties(sku, response);
//                    // 设置二级结构的ID和PID
//                    response.setId(sku.getSkuId());  // id = skuId
//                    response.setPid(spuId); // pid = spuId
//                    response.setHasChildren(true);
//                    return response;
//                })
//                .collect(Collectors.toList());
//
//        // 创建分页结果
//        IPage<SpuGroupViewResponse> resultPage = new Page<>(
//                skuPage.getCurrent(),
//                skuPage.getSize(),
//                skuPage.getTotal()
//        );
//        resultPage.setRecords(skuResponses);
//
//        return resultPage;
//    }
//
//    /**
//     * 为一批SKU数据附加校准标记
//     *
//     * @param skuMasterViewList 从数据库查出的主数据列表
//     */
//    private void attachCalibrationTags(List<SkuMasterViewDTO> skuMasterViewList) {
//        if (CollectionUtils.isEmpty(skuMasterViewList)) {
//            return;
//        }
//
//        // 提取所有需要查询标记的 offer ID
//        List<Long> offerIds = skuMasterViewList.stream()
//                .map(SkuMasterViewDTO::getOfferId)
//                .distinct()
//                .toList();
//
//        // 批量从Redis获取缓存
//        List<String> redisKeys = offerIds.stream()
//                .map(id -> RedisKeys.CALIBRATION_TAGS_OFFER + id)
//                .collect(Collectors.toList());
//
//        // 缓存批量获取标记的字段
//        List<Map<String, Integer>> cachedTagsList = redisUtils.multiGetCacheObject(redisKeys);
//
//        Map<Long, Map<String, Integer>> cachedTagsMap = new HashMap<>();
//
//        // 获取缓存命中的ID
//        List<Long> cacheMissOfferIds = getMissOfferIds(offerIds, cachedTagsList, cachedTagsMap);
//
//
//        //  对缓存未命中的ID，批量查询数据库
//        if (!cacheMissOfferIds.isEmpty()) {
//
//            // 查询到db中标记的tag
//            List<DataCalibrationTags> dbTags = calibrationService.getTagsByOfferId("offers", cacheMissOfferIds);
//
//            // 按 offerId 对数据库查询结果进行分组
//            Map<Long, List<DataCalibrationTags>> dbTagsGrouped = dbTags.stream()
//                    .collect(Collectors.groupingBy(DataCalibrationTags::getTargetId));
//
//            // 处理数据库结果，并写回缓存
//            for (Long offerId : cacheMissOfferIds) {
//                List<DataCalibrationTags> tagsForOffer = dbTagsGrouped.get(offerId);
//                Map<String, Integer> tagsToCache = new HashMap<>();
//                if (!CollectionUtils.isEmpty(tagsForOffer)) {
//                    tagsToCache = tagsForOffer.stream()
//                            .collect(Collectors.toMap(DataCalibrationTags::getFieldName, DataCalibrationTags::getTagStatus));
//                }
//
//                // 存入本次查询结果的map中
//                cachedTagsMap.put(offerId, tagsToCache);
//                // 写回Redis缓存，即使是空结果也缓存，防止缓存穿透
//                String redisKey = RedisKeys.CALIBRATION_TAGS_OFFER + offerId;
//                redisUtils.setCacheObject(redisKey, tagsToCache, 1L, TimeUnit.HOURS);
//            }
//        }
//
//        // 将获取到的所有标记
//        for (SkuMasterViewDTO skuDto : skuMasterViewList) {
//            Map<String, Integer> tags = cachedTagsMap.get(skuDto.getOfferId());
//            skuDto.setCalibrationTags(tags != null ? tags : Collections.emptyMap());
//        }
//    }
//
//    private static List<Long> getMissOfferIds(List<Long> offerIds, List<Map<String, Integer>> cachedTagsList, Map<Long, Map<String, Integer>> cachedTagsMap) {
//        List<Long> cacheMissOfferIds = new ArrayList<>();
//
//        // 遍历批量获取的结果
//        for (int i = 0; i < offerIds.size(); i++) {
//            Long offerId = offerIds.get(i);
//            Map<String, Integer> tags = (cachedTagsList != null && cachedTagsList.size() > i) ? cachedTagsList.get(i) : null;
//            if (tags != null) {
//                cachedTagsMap.put(offerId, tags);
//            } else {
//                cacheMissOfferIds.add(offerId);
//            }
//        }
//        return cacheMissOfferIds;
//    }
//
//    /**
//     * 将 spuId 对应的 SkuMasterViewDTO 列表转换为 SpuGroupViewResponse
//     */
////    private SpuGroupViewResponse buildSpuGroupView(Map.Entry<String, List<SkuMasterViewDTO>> entry) {
////        List<SkuMasterViewDTO> valueList = entry.getValue();
////        List<ProductDataViewResponse> skuList = Lists.newArrayList();
////
////        SpuGroupViewResponse group = new SpuGroupViewResponse();
////        group.setSpuId(entry.getKey());
////        for (SkuMasterViewDTO skuMasterViewDTO : valueList) {
////            ProductDataViewResponse dataViewResponse = new ProductDataViewResponse();
////            BeanUtils.copyProperties(skuMasterViewDTO, dataViewResponse);
////            skuList.add(dataViewResponse);
////        }
////        group.setSkuList(skuList);
////
////        // 设置SPU级别的通用信息
////        entry.getValue().stream().filter(Objects::nonNull).findFirst().ifPresent(first -> {
//////            group.setModel(first.getModel());
//////            group.setColor(first.getColor());
//////            group.setStorage(first.getStorage());
//////            group.setServiceProvider(first.getServiceProvider());
//////            group.setCondition(first.getConditionNew());
//////            group.setTitle(first.getTitle());
////            BeanUtils.copyProperties(first, group);
////        });
////
////        return group;
////    }
//
//    /**
//     * 根据skuId获取全平台报价
//     */
//    public List<ProductDataViewResponse> getAllPlatformOffersBySkuId(String skuId) {
//        List<ProductDataViewResponse> offers = productViewMapper.selectAllPlatformOffers(skuId);
//        if (CollectionUtils.isEmpty(offers)) {
//            return Collections.emptyList();
//        }
//        List<SkuMasterViewDTO> tempSkus = offers.stream()
//                .map(dto -> {
//                    SkuMasterViewDTO sku = new SkuMasterViewDTO();
//                    BeanUtils.copyProperties(dto, sku);
//                    return sku;
//                })
//                .collect(Collectors.toList());
//
//        // 复用现有逻辑附加校准标签
//        attachCalibrationTags(tempSkus);
//
//        for (ProductDataViewResponse response : offers) {
//            Optional<SkuMasterViewDTO> matched = tempSkus.stream()
//                    .filter(sku -> Objects.equals(sku.getOfferId(), response.getOfferId()))
//                    .findFirst();
//            matched.ifPresent(sku -> response.setCalibrationTags(sku.getCalibrationTags()));
//        }
//
//        return offers;
//    }
//
//    /**
//     * 为SPU数据附加校准标记
//     *
//     * @param spuList SPU列表
//     */
//    private void attachCalibrationTagsForSpu(List<SpuGroupViewResponse> spuList) {
//        if (CollectionUtils.isEmpty(spuList)) {
//            return;
//        }
//
//        // 提取所有需要查询标记的 SPU ID (这里使用offerId作为simplify表的主键)
//        List<Long> spuOfferIds = spuList.stream()
//                .map(SpuGroupViewResponse::getOfferId)
//                .distinct()
//                .collect(Collectors.toList());
//
//        // 批量从Redis获取缓存
//        List<String> redisKeys = spuOfferIds.stream()
//                .map(id -> RedisKeys.CALIBRATION_TAGS_SIMPLIFY + id)
//                .collect(Collectors.toList());
//
//        // 缓存批量获取标记的字段
//        List<Map<String, Integer>> cachedTagsList = redisUtils.multiGetCacheObject(redisKeys);
//
//        Map<Long, Map<String, Integer>> cachedTagsMap = new HashMap<>();
//
//        // 获取缓存未命中的ID
//        List<Long> cacheMissOfferIds = getMissOfferIds(spuOfferIds, cachedTagsList, cachedTagsMap);
//
//        // 对缓存未命中的ID，批量查询数据库
//        if (!cacheMissOfferIds.isEmpty()) {
//            // 查询simplify表中标记的tag
//            List<DataCalibrationTags> dbTags = calibrationService.getTagsByOfferId("simplify", cacheMissOfferIds);
//
//            // 按 offerId 对数据库查询结果进行分组
//            Map<Long, List<DataCalibrationTags>> dbTagsGrouped = dbTags.stream()
//                    .collect(Collectors.groupingBy(DataCalibrationTags::getTargetId));
//
//            // 处理数据库结果，并写回缓存
//            for (Long offerId : cacheMissOfferIds) {
//                List<DataCalibrationTags> tagsForOffer = dbTagsGrouped.get(offerId);
//                Map<String, Integer> tagsToCache = new HashMap<>();
//                if (!CollectionUtils.isEmpty(tagsForOffer)) {
//                    tagsToCache = tagsForOffer.stream()
//                            .collect(Collectors.toMap(DataCalibrationTags::getFieldName, DataCalibrationTags::getTagStatus));
//                }
//
//                // 存入本次查询结果的map中
//                cachedTagsMap.put(offerId, tagsToCache);
//                // 写回Redis缓存，即使是空结果也缓存，防止缓存穿透
//                String redisKey = RedisKeys.CALIBRATION_TAGS_SIMPLIFY + offerId;
//                redisUtils.setCacheObject(redisKey, tagsToCache, 1L, TimeUnit.HOURS);
//            }
//        }
//
//        // 将查询到的标记数据附加到每个SPU对象上
//        for (SpuGroupViewResponse spu : spuList) {
//            Map<String, Integer> tags = cachedTagsMap.get(spu.getOfferId());
//            spu.setCalibrationTags(tags != null ? tags : new HashMap<>());
//        }
//    }
//
//    public void updateCalibrationTags(List<CalibrationTagRequest> dtoList) {
//        if (CollectionUtils.isEmpty(dtoList)) {
//            throw new BizException(ResultCode.PARAM_INVALIDATE);
//        }
//        log.info("开始标记更新 Calibration Tags: {}", JSON.toJSONString(dtoList));
//        calibrationService.batchSaveOrUpdateTags(dtoList);
//
//        // 根据 targetTable 清除对应的缓存
//        dtoList.forEach(dto -> {
//            String cacheKey = getCacheKeyByTargetTable(dto.getTargetTable(), dto.getTargetId());
//            redisUtils.deleteObject(cacheKey);
//            log.debug("清除缓存: {}", cacheKey);
//        });
//    }
//
//    /**
//     * 根据目标表名获取对应的缓存Key
//     */
//    private String getCacheKeyByTargetTable(String targetTable, Long targetId) {
//        if ("offers".equals(targetTable) || "product_data_offers".equals(targetTable)) {
//            return RedisKeys.CALIBRATION_TAGS_OFFER + targetId;
//        } else if ("simplify".equals(targetTable) || "product_data_simplify".equals(targetTable)) {
//            return RedisKeys.CALIBRATION_TAGS_SIMPLIFY + targetId;
//        } else {
//            log.warn("未知的目标表名: {}, 使用默认的offer缓存Key", targetTable);
//            return RedisKeys.CALIBRATION_TAGS_OFFER + targetId;
//        }
//    }
//
//}
