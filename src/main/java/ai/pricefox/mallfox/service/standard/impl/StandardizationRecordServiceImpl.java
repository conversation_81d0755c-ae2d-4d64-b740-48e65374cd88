package ai.pricefox.mallfox.service.standard.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.standard.StandardizationRecord;
import ai.pricefox.mallfox.mapper.standard.StandardizationRecordMapper;
import ai.pricefox.mallfox.service.standard.StandardizationRecordService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class StandardizationRecordServiceImpl extends ServiceImpl<StandardizationRecordMapper, StandardizationRecord> implements StandardizationRecordService {
    private final StandardizationRecordMapper standardizationRecordMapper;

    public StandardizationRecordServiceImpl(StandardizationRecordMapper standardizationRecordMapper) {
        this.standardizationRecordMapper = standardizationRecordMapper;
    }

    @Override
    public StandardizationRecord queryByDataId(String dataId) {
        if (dataId == null) {
            return null;
        }
        return standardizationRecordMapper.selectOne(new LambdaQueryWrapper<StandardizationRecord>().eq(StandardizationRecord::getDataId, dataId));
    }
}
