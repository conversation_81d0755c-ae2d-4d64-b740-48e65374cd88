package ai.pricefox.mallfox.service.standard.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import ai.pricefox.mallfox.domain.standard.InvalidValuePatterns;
import ai.pricefox.mallfox.service.standard.InvalidValuePatternsService;
import ai.pricefox.mallfox.mapper.standard.InvalidValuePatternsMapper;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class InvalidValuePatternsServiceImpl extends ServiceImpl<InvalidValuePatternsMapper, InvalidValuePatterns> implements InvalidValuePatternsService {

}




