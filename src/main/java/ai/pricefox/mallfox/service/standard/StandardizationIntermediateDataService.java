package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardizationIntermediateData;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标准化中间数据Service接口
 */
public interface StandardizationIntermediateDataService extends IService<StandardizationIntermediateData> {
    /**
     * 根据产品标识符获取中间数据
     * @param productIdentifier 产品标识符
     * @return 标准化中间数据
     */
    StandardizationIntermediateData getByProductIdentifier(String productIdentifier);

    /**
     * 保存或更新中间数据
     * @param data 标准化中间数据
     */
    void saveOrUpdateIntermediateData(StandardizationIntermediateData data);
}