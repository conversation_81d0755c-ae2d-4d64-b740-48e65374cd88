package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardizationRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 */
public interface StandardizationRecordService extends IService<StandardizationRecord> {
    /**
     * 根据dataId查询
     *
     * @param dataId 源数据ID
     * @return 标准化记录
     */
    StandardizationRecord queryByDataId(String dataId);
}
