package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.domain.standard.TripartitePlatform;
import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper;
import ai.pricefox.mallfox.service.standard.TripartitePlatformService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class TripartitePlatformServiceImpl extends ServiceImpl<TripartitePlatformMapper, TripartitePlatform> implements TripartitePlatformService {

    private final TripartitePlatformMapper tripartitePlatformMapper;

    public TripartitePlatformServiceImpl(TripartitePlatformMapper tripartitePlatformMapper) {
        this.tripartitePlatformMapper = tripartitePlatformMapper;
    }

    @Override
    public TripartitePlatform getByPlatformCode(ProductPlatformEnum platformName, DataChannelEnum dataChannelEnum) {
        return tripartitePlatformMapper.selectOne(new LambdaQueryWrapper<TripartitePlatform>().eq(TripartitePlatform::getPlatformName, platformName.name()).eq(TripartitePlatform::getSourceType, dataChannelEnum.name()));
    }
}




