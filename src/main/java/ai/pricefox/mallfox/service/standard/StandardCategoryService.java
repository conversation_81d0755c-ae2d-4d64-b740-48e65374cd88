package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.CategoryInfoCreateReqVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoPageReqVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoRespVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoUpdateReqVO;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标准品类库Service接口
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface StandardCategoryService extends IService<StandardCategory> {

    /**
     * 创建分类信息
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<CategoryInfoRespVO> createCategoryInfo(CategoryInfoCreateReqVO reqVO);

    /**
     * 更新分类信息
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<CategoryInfoRespVO> updateCategoryInfo(CategoryInfoUpdateReqVO reqVO);

    /**
     * 根据ID获取分类信息
     *
     * @param id 分类ID
     * @return 分类信息
     */
    CommonResult<CategoryInfoRespVO> getCategoryInfoById(Long id);

    /**
     * 分页查询分类信息
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    CommonResult<PageResult<CategoryInfoRespVO>> getCategoryInfoPage(CategoryInfoPageReqVO reqVO);

    /**
     * 删除分类信息
     *
     * @param id 分类ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteCategoryInfo(Long id);

    /**
     * 获取所有分类信息列表
     *
     * @return 分类信息列表
     */
    CommonResult<List<CategoryInfoRespVO>> getAllCategoryInfos();

    /**
     * 获取分类树形结构
     *
     * @return 分类树
     */
    CommonResult<List<Tree<Long>>> getCategoryInfoTree();

    /**
     * 根据父分类ID获取子分类列表
     *
     * @param parentId 父分类ID
     * @return 子分类列表
     */
    CommonResult<List<CategoryInfoRespVO>> getCategoryInfosByParentId(Long parentId);
}