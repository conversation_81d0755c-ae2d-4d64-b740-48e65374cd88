package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.ChangeLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 变更日志Service接口
 */
public interface ChangeLogService extends IService<ChangeLog> {
    /**
     * 记录原始数据变更
     * @param productData 产品数据
     * @param changeType 变更类型
     * @param operator 操作人
     */
    void recordRawDataChange(Object productData, String changeType, String operator);

    /**
     * 记录数据合并变更
     * @param productIdentifier 产品标识符
     * @param platform 平台
     * @param changeType 变更类型
     * @param operator 操作人
     */
    void recordDataMergeChange(String productIdentifier, String platform, String changeType, String operator);

    /**
     * 记录人工字段变更
     * @param productIdentifier 产品标识符
     * @param fieldName 字段名
     * @param originalValue 原始值
     * @param newValue 新值
     * @param operator 操作人
     */
    void recordManualFieldChange(String productIdentifier, String fieldName, String originalValue, String newValue, String operator);
}