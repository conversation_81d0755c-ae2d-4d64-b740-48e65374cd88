package ai.pricefox.mallfox.service.standard.impl;

import ai.pricefox.mallfox.common.exception.enums.ErrorCodeConstants;
import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import ai.pricefox.mallfox.mapper.standard.StandardBrandMapper;
import ai.pricefox.mallfox.mapper.standard.StandardCategoryMapper;
import ai.pricefox.mallfox.service.standard.StandardCategoryService;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.category.CategoryInfoCreateReqVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoPageReqVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoRespVO;
import ai.pricefox.mallfox.vo.category.CategoryInfoUpdateReqVO;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static ai.pricefox.mallfox.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 标准品类库Service实现类
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
@AllArgsConstructor
public class StandardCategoryServiceImpl extends ServiceImpl<StandardCategoryMapper, StandardCategory> implements StandardCategoryService {

    private final StandardCategoryMapper standardCategoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryInfoRespVO> createCategoryInfo(CategoryInfoCreateReqVO reqVO) {
        // 检查分类编码是否已存在
        LambdaQueryWrapper<StandardCategory> codeWrapper = new LambdaQueryWrapper<>();
        codeWrapper.eq(StandardCategory::getCategoryCode, generateCategoryCode(reqVO.getName()));
        StandardCategory existingByCode = standardCategoryMapper.selectOne(codeWrapper);
        if (existingByCode != null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NAME_EXISTS);
        }

        // 检查分类名称是否已存在
        LambdaQueryWrapper<StandardCategory> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.eq(StandardCategory::getCategoryNameEn, reqVO.getName())
                .or()
                .eq(StandardCategory::getCategoryNameCn, reqVO.getName());
        StandardCategory existingByName = standardCategoryMapper.selectOne(nameWrapper);
        if (existingByName != null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NAME_EXISTS);
        }

        // 验证父分类存在性
        if (reqVO.getParentId() != null && reqVO.getParentId() > 0) {
            StandardCategory parentCategory = standardCategoryMapper.selectById(reqVO.getParentId());
            if (parentCategory == null) {
                throw exception(ErrorCodeConstants.CATEGORY_INFO_PARENT_NOT_EXIST);
            }
        }

        StandardCategory standardCategory = new StandardCategory();
        standardCategory.setCategoryCode(generateCategoryCode(reqVO.getName()));
        standardCategory.setCategoryNameEn(reqVO.getName());
        standardCategory.setCategoryNameCn(reqVO.getName());
        standardCategory.setLevel(reqVO.getLevel());
        standardCategory.setIconUrl(reqVO.getIconUrl());
        standardCategory.setSort(reqVO.getSortOrder() != null ? reqVO.getSortOrder() : 0);
        standardCategory.setIsActive(reqVO.getIsActive() != null ? reqVO.getIsActive() : true);
        standardCategory.setParent(reqVO.getParentId() != null ? reqVO.getParentId().intValue() : 0);
        standardCategory.setCreateDate(LocalDateTime.now());
        standardCategory.setCreateUsername("system");
        
        // 插入数据
        standardCategoryMapper.insert(standardCategory);

        CategoryInfoRespVO respVO = convertToRespVO(standardCategory);
        return CommonResult.success(respVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<CategoryInfoRespVO> updateCategoryInfo(CategoryInfoUpdateReqVO reqVO) {
        // 检查分类是否存在
        StandardCategory existingCategory = standardCategoryMapper.selectById(reqVO.getId());
        if (existingCategory == null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NOT_EXIST);
        }

        // 检查分类名称是否已被其他分类使用
        LambdaQueryWrapper<StandardCategory> nameWrapper = new LambdaQueryWrapper<>();
        nameWrapper.ne(StandardCategory::getId, reqVO.getId())
                .and(wrapper -> wrapper.eq(StandardCategory::getCategoryNameEn, reqVO.getName())
                        .or()
                        .eq(StandardCategory::getCategoryNameCn, reqVO.getName()));
        StandardCategory categoryWithSameName = standardCategoryMapper.selectOne(nameWrapper);
        if (categoryWithSameName != null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NAME_EXISTS);
        }

        // 验证父分类存在性（不能设置自己为父分类）
        if (reqVO.getParentId() != null && reqVO.getParentId() > 0) {
            if (reqVO.getParentId().equals(reqVO.getId())) {
                throw exception(ErrorCodeConstants.CATEGORY_INFO_PARENT_ERROR);
            }
            StandardCategory parentCategory = standardCategoryMapper.selectById(reqVO.getParentId());
            if (parentCategory == null) {
                throw exception(ErrorCodeConstants.CATEGORY_INFO_PARENT_NOT_EXIST);
            }
        }

        // 更新分类信息
        existingCategory.setCategoryNameEn(reqVO.getName());
        existingCategory.setCategoryNameCn(reqVO.getName());
        existingCategory.setLevel(reqVO.getLevel());
        existingCategory.setIconUrl(reqVO.getIconUrl()); 
        existingCategory.setSort(reqVO.getSortOrder());
        existingCategory.setIsActive(reqVO.getIsActive());
        existingCategory.setParent(reqVO.getParentId() != null ? reqVO.getParentId().intValue() : 0);
        existingCategory.setUpdateDate(LocalDateTime.now());
        existingCategory.setUpdateUsername("system");
        
        standardCategoryMapper.updateById(existingCategory);

        CategoryInfoRespVO respVO = convertToRespVO(existingCategory);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<CategoryInfoRespVO> getCategoryInfoById(Long id) {
        StandardCategory standardCategory = standardCategoryMapper.selectById(id);
        if (standardCategory == null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NOT_EXIST);
        }

        CategoryInfoRespVO respVO = convertToRespVO(standardCategory);
        return CommonResult.success(respVO);
    }

    @Override
    public CommonResult<PageResult<CategoryInfoRespVO>> getCategoryInfoPage(CategoryInfoPageReqVO reqVO) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        
        // 添加查询条件
        if (StringUtils.hasText(reqVO.getName())) {
            wrapper.like(StandardCategory::getCategoryNameEn, reqVO.getName())
                    .or()
                    .like(StandardCategory::getCategoryNameCn, reqVO.getName())
                    .or()
                    .like(StandardCategory::getCategoryCode, reqVO.getName());
        }
        
        if (reqVO.getLevel() != null) {
            wrapper.eq(StandardCategory::getLevel, reqVO.getLevel());
        }
        
        if (reqVO.getParentId() != null) {
            wrapper.eq(StandardCategory::getParent, reqVO.getParentId());
        }
        
        if (reqVO.getIsActive() != null) {
            wrapper.eq(StandardCategory::getIsActive, reqVO.getIsActive());
        }
        
        // 按排序字段和创建时间排序
        wrapper.orderByAsc(StandardCategory::getSort)
                .orderByDesc(StandardCategory::getCreateDate);

        Page<StandardCategory> page = new Page<>(reqVO.getPageNo(), reqVO.getPageSize());
        Page<StandardCategory> result = standardCategoryMapper.selectPage(page, wrapper);
        
        List<CategoryInfoRespVO> respVOList = result.getRecords().stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());

        PageResult<CategoryInfoRespVO> pageResult = new PageResult<>(respVOList, result.getTotal());
        return CommonResult.success(pageResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deleteCategoryInfo(Long id) {
        // 检查分类是否存在
        StandardCategory standardCategory = standardCategoryMapper.selectById(id);
        if (standardCategory == null) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_NOT_EXIST);
        }

        // 检查是否有子分类
        LambdaQueryWrapper<StandardCategory> childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(StandardCategory::getParent, id);
        List<StandardCategory> childCategories = standardCategoryMapper.selectList(childWrapper);
        if (!childCategories.isEmpty()) {
            throw exception(ErrorCodeConstants.CATEGORY_INFO_HAS_CHILDREN);
        }

        // 删除分类
        int result = standardCategoryMapper.deleteById(id);
        return CommonResult.success(result > 0);
    }

    @Override
    public CommonResult<List<CategoryInfoRespVO>> getAllCategoryInfos() {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getIsActive, true)
                .orderByAsc(StandardCategory::getSort)
                .orderByDesc(StandardCategory::getCreateDate);
        
        List<StandardCategory> categoryList = standardCategoryMapper.selectList(wrapper);
        
        List<CategoryInfoRespVO> respVOList = categoryList.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());

        return CommonResult.success(respVOList);
    }

    @Override
    public CommonResult<List<Tree<Long>>> getCategoryInfoTree() {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getIsActive, true)
                .orderByAsc(StandardCategory::getSort);
        
        List<StandardCategory> categoryList = standardCategoryMapper.selectList(wrapper);
        
        List<TreeNode<Long>> treeNodes = categoryList.stream()
                .map(c -> new TreeNode<>(c.getId().longValue(), 
                        c.getParent().longValue(), 
                        StringUtils.hasText(c.getCategoryNameCn()) ? c.getCategoryNameCn() : c.getCategoryNameEn(), 
                        c.getSort()))
                .collect(Collectors.toList());

        List<Tree<Long>> tree = TreeUtil.build(treeNodes, 0L);
        return CommonResult.success(tree);
    }

    @Override
    public CommonResult<List<CategoryInfoRespVO>> getCategoryInfosByParentId(Long parentId) {
        LambdaQueryWrapper<StandardCategory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StandardCategory::getParent, parentId)
                .eq(StandardCategory::getIsActive, true)
                .orderByAsc(StandardCategory::getSort)
                .orderByDesc(StandardCategory::getCreateDate);
        
        List<StandardCategory> categoryList = standardCategoryMapper.selectList(wrapper);
        
        List<CategoryInfoRespVO> respVOList = categoryList.stream()
                .map(this::convertToRespVO)
                .collect(Collectors.toList());

        return CommonResult.success(respVOList);
    }

    /**
     * 生成分类编码
     */
    private String generateCategoryCode(String categoryName) {
        // 简单的编码生成策略：分类名称的拼音首字母 + 时间戳后4位
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = timestamp.substring(timestamp.length() - 4);
        return categoryName.toUpperCase().replaceAll("[^A-Z0-9]", "").substring(0, Math.min(categoryName.length(), 10)) + suffix;
    }

    /**
     * 转换为响应VO
     */
    private CategoryInfoRespVO convertToRespVO(StandardCategory standardCategory) {
        CategoryInfoRespVO respVO = new CategoryInfoRespVO();
        respVO.setId(standardCategory.getId().longValue());
        respVO.setParentId(standardCategory.getParent().longValue());
        respVO.setName(StringUtils.hasText(standardCategory.getCategoryNameCn()) ? 
                standardCategory.getCategoryNameCn() : standardCategory.getCategoryNameEn());
        respVO.setLevel(standardCategory.getLevel());
        respVO.setIconUrl(standardCategory.getIconUrl());
        respVO.setSortOrder(standardCategory.getSort());
        respVO.setIsActive(standardCategory.getIsActive());
        respVO.setCreateTime(standardCategory.getCreateDate());
        respVO.setUpdateTime(standardCategory.getUpdateDate());
        return respVO;
    }
}