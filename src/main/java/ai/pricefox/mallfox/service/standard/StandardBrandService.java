package ai.pricefox.mallfox.service.standard;

import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.domain.standard.StandardColor;
import ai.pricefox.mallfox.vo.base.CommonResult;
import ai.pricefox.mallfox.vo.base.PageResult;
import ai.pricefox.mallfox.vo.brand.BrandInfoCreateReqVO;
import ai.pricefox.mallfox.vo.brand.BrandInfoPageReqVO;
import ai.pricefox.mallfox.vo.brand.BrandInfoRespVO;
import ai.pricefox.mallfox.vo.brand.BrandInfoUpdateReqVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 标准品牌库Service接口
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface StandardBrandService extends IService<StandardBrand> {

    /**
     * 创建品牌信息
     *
     * @param reqVO 创建请求
     * @return 创建结果
     */
    CommonResult<BrandInfoRespVO> createBrandInfo(BrandInfoCreateReqVO reqVO);

    /**
     * 更新品牌信息
     *
     * @param reqVO 更新请求
     * @return 更新结果
     */
    CommonResult<BrandInfoRespVO> updateBrandInfo(BrandInfoUpdateReqVO reqVO);

    /**
     * 根据ID获取品牌信息
     *
     * @param id 品牌ID
     * @return 品牌信息
     */
    CommonResult<BrandInfoRespVO> getBrandInfoById(Long id);

    /**
     * 分页查询品牌信息
     *
     * @param reqVO 分页查询请求
     * @return 分页结果
     */
    CommonResult<PageResult<BrandInfoRespVO>> getBrandInfoPage(BrandInfoPageReqVO reqVO);

    /**
     * 获取所有品牌信息列表
     *
     * @return 品牌信息列表
     */
    CommonResult<List<BrandInfoRespVO>> getAllBrandInfos();

    /**
     * 删除品牌信息
     *
     * @param id 品牌ID
     * @return 删除结果
     */
    CommonResult<Boolean> deleteBrandInfo(Long id);
}