package ai.pricefox.mallfox.model.dto;

import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * 动态标准产品类
 * 使用Map存储所有标准字段的值，键为StandardField中的field_name_en
 */
@Data
public class DynamicStandardProduct {
    /**
     * 动态字段映射
     * key: StandardField.field_name_en (如: "Title", "Brand", "Model"等)
     * value: 字段值
     *  获取所有字段
     */
    private Map<String, Object> fields = new HashMap<>();

    /**
     * 平台名称
     */
    private ProductPlatformEnum platformName;

    /**
     * 平台代码
     */
    private String platformCode;

    /**
     * 数据来源
     */
    private DataChannelEnum sourceType;

    /**
     * SKU ID
     */
    private String skuId;

    /**
     * 设置字段值
     *
     * @param fieldName StandardField.field_name_en
     * @param value     字段值
     */
    public void setField(String fieldName, Object value) {
        fields.put(fieldName, value);
    }


    /**
     * 获取字符串类型的字段值
     *
     * @param fieldName StandardField.field_name_en
     * @return 字符串字段值
     */
    public String getStringField(String fieldName) {
        Object value = fields.get(fieldName);
        return value != null ? value.toString() : null;
    }

    /**
     * 检查是否存在某个字段
     *
     * @param fieldName StandardField.field_name_en
     * @return 是否存在
     */
    public boolean hasField(String fieldName) {
        return fields.containsKey(fieldName);
    }

    /**
     * 检查字段是否为空
     *
     * @param fieldName StandardField.field_name_en
     * @return 字段是否为空
     */
    public boolean isFieldEmpty(String fieldName) {
        Object value = fields.get(fieldName);
        if (value == null) {
            return true;
        }
        if (value instanceof String) {
            return ((String) value).trim().isEmpty();
        }
        return false;
    }

    /**
     * 检查是否存在指定字段且不为空
     *
     * @param fieldName 字段名
     * @return 是否存在且不为空
     */
    public boolean hasFieldWithValue(String fieldName) {
        return hasField(fieldName) && !isFieldEmpty(fieldName);
    }
}
