package ai.pricefox.mallfox.model.dto;


import ai.pricefox.mallfox.enums.DataChannelEnum;
import ai.pricefox.mallfox.enums.ProductPlatformEnum;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 商品核心数据BO
 * @since 2025/6/23
 */
@Data
public class ProductDataDTO {

    /**
     * 是否强制更新 默认不强制 1强制刷新，在后期修正低频数据使用
     */
    private Integer forceUpdate = 0;

    /**
     * 商品规格(高频-低频)
     */
    @NotBlank(message = "商品规格不能为空")
    private String model;

    /**
     * 处理后的商品规格（去除品牌信息后的型号，用于匹配）
     */
    private String processedModel;

    /**
     * 商品颜色(高频-低频)
     */
    @NotBlank(message = "商品颜色不能为空")
    private String color;

    /**
     * 商品存储(高频-低频)
     */
    @NotBlank(message = "商品存储不能为空")
    private String storage;

    /**
     * 商品服务商(高频-低频)
     */
    @NotBlank(message = "商品服务商不能为空")
    private String serviceProvider;

    /**
     * 商品状态(高频-低频)
     */
    @NotBlank(message = "商品状态不能为空")
    private String condition;

    /**
     * 系列商品状态(高频)
     */
    private String series;

    /**
     * UPC编码(高频)
     */
    private String upcCode;

    /**
     * 商品来源平台(高频-低频)
     */
    @NotBlank(message = "商品来源平台不能为空")
    private ProductPlatformEnum sourcePlatform;
    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台SPU ID(高频-低频)
     */
    @NotBlank(message = "平台商品的SPU-ID不能为空")
    private String platformSpuId;

    /**
     * 平台SKU ID(高频-低频)
     */
    @NotBlank(message = "平台商品的SKU-ID不能为空")
    private String platformSkuId;

    /**
     * 商品链接地址(高频)
     */

    private String itemUrl;

    /**
     * 商品标题(高频)
     */
    private String title;

    /**
     * 品牌(高频)
     */
    private String brand;

    /**
     * 当前价格(高频)
     */
    private BigDecimal price;

    /**
     * 列表价(高频)
     */
    private BigDecimal listPrice;

    /**
     * 折扣(高频)
     */
    private BigDecimal discount;

    /**
     * 库存(高频)
     */
    private String inventory;

    /**
     * 最近30天销量(高频)
     */
    private String salesLast30Days;

    /**
     * 卖家信息(高频)
     */
    private String seller;

    /**
     * 商家评分(高频)
     */
    private BigDecimal merchantRating;

    /**
     * 款式年份(低频)
     */
    private String modelYear;

    /**
     * 操作系统(低频)
     */
    private String operatingSystem;

    /**
     * 屏幕尺寸(低频)
     */
    private String screenSize;

    /**
     * 商品主图URL(低频)
     */
    private String productMainImageUrls;

    /**
     * 规格对应颜色图片URL(低频)
     */
    private String productSpecColorUrl;

    /**
     * 安装内存大小(低频)
     */
    private String ramMemoryInstalledSize;

    /**
     * 处理器型号(低频)
     */
    private String processor;

    /**
     * 移动网络技术(低频)
     */
    private String cellularTechnology;

    /**
     * 分辨率(低频)
     */
    private String resolution;

    /**
     * 刷新率(低频)
     */
    private String refreshRate;

    /**
     * 显示屏类型(低频)
     */
    private String displayType;

    /**
     * 电池容量(低频)
     */
    private String batteryPower;

    /**
     * 平均通话时间(低频)
     */
    private String averageTalkTime;

    /**
     * 电池充电时间(低频)
     */
    private String batteryChargeTime;

    /**
     * 前置摄像头分辨率(低频)
     */
    private String frontPhotoSensorResolution;

    /**
     * 后置摄像头分辨率(低频)
     */
    private String rearFacingCameraPhotoSensorResolution;

    /**
     * 后置摄像头数量(低频)
     */
    private Integer numberOfRearFacingCameras;

    /**
     * 有效视频分辨率(低频)
     */
    private String effectiveVideoResolution;

    /**
     * 视频捕捉分辨率(低频)
     */
    private String videoCaptureResolution;

    /**
     * SIM卡槽数量(低频)
     */
    private String simCardSlotCount;

    /**
     * 接口类型(低频)
     */
    private String connectorType;

    /**
     * 防水等级(低频)
     */
    private String waterResistance;

    /**
     * 产品尺寸(低频)
     */
    private String dimensions;

    /**
     * 商品重量(低频)
     */
    private String itemWeight;

    /**
     * 生物识别安全功能(低频)
     */
    private String biometricSecurityFeature;

    /**
     * 支持的卫星导航系统(低频)
     */
    private String supportedSatelliteNavigationSystem;

    /**
     * 其他特征描述(低频)
     */
    private String features;

    /**
     * 退货政策(低频)
     */
    private String returnPolicy;

    /**
     * 是否支持分期付款(低频)
     */
    private String paymentInstallment;

    /**
     * 安装支付方式(低频)
     */
    private String installPayment;

    /**
     * 保修说明(低频)
     */
    private String warrantyDescription;

    /**
     * 评论数量(低频)
     */
    private Integer reviewNumber;

    /**
     * 评论评分(低频)
     */
    private BigDecimal reviewScore;

    /**
     * 评分分布(低频)
     */
    private String reviewRatingDistribution;

    /**
     * 维度评分(低频)
     */
    private String reviewDimensionalRatings;

    /**
     * 评论概览（优缺点）(低频)
     */
    private String reviewOverviewProsCons;

    /**
     * 按星级分类的评价优缺点(低频)
     */
    private String reviewProsConsByStar;

    /**
     * 一级分类(高频)
     */
    private String categoryLevel1;

    /**
     * 二级分类(高频)
     */
    private String categoryLevel2;

    /**
     * 三级分类(高频)
     */
    private String categoryLevel3;

    /**
     * 发货时间(低频)
     */
    private String shippingTime;

    /**
     * 价格更新时间(高频)
     */
    private String priceUpdateTime;

    /**
     * 数据渠道： 1 爬虫 2 API
     * {@link ai.pricefox.mallfox.enums.DataChannelEnum#CRAWLER} 爬虫  {@link ai.pricefox.mallfox.enums.DataChannelEnum#API} API
     */
    @NotBlank(message = "数据渠道不能为空")
    private DataChannelEnum dataChannel;

    /**
     * 制造商
     */
    private String manufacturer;
    
    /**
     * 保修期（人工）
     */
    private String warrantyLabor;
    
    /**
     * 保修零件（人工）
     */
    private String warrantyParts;

}
