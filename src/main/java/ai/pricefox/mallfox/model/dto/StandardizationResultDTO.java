package ai.pricefox.mallfox.model.dto;

import java.util.Map;

import ai.pricefox.mallfox.enums.FieldMappingStatusEnum;
import lombok.Data;

/**
 * 标准化结果数据传输对象
 */
@Data
public class StandardizationResultDTO {
    /**
     * 源数据
     */
    private ProductDataDTO originalData;
    /**
     * 标准化后的数据
     */
    private Map<String, Object> standardizedData;

    /**
     * 字段映射状态
     */
    private Map<String, FieldMappingStatusEnum> fieldMappingStatus;

    /**
     * 所有标准字段
     */
    private Map<String, String> allStandardFields;

    /**
     * 匹配字段数量
     */
    private int matchedFieldsCount;

    /**
     * 未匹配字段数量
     */
    private int unmatchedFieldsCount;

    /**
     * 空字段数量
     */
    private int emptyFieldsCount;

    /**
     * 总字段数量
     */
    private int totalFieldsCount;

}
