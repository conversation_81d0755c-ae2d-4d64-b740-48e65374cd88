package ai.pricefox.mallfox.model.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 商品首页接口返回
 * @since 2025/7/7
 */
@Data
public class ProductCardDTO {
    /**
     * 自建的SKU ID
     */
    private String skuId;

    /**
     * 商品完整标题 (SPU名 + SKU属性)
     */
    private String title;

    /**
     * 商品卡片主图URL
     */
    private String imageUrl;

    /**
     * 当前最低售价
     */
    private BigDecimal price;

    /**
     * 划线价/原价
     */
    private BigDecimal listPrice;

    /**
     * 平均评分
     */
    private BigDecimal rating;

    /**
     * 分期付款信息
     */
    private String installmentInfo;
}