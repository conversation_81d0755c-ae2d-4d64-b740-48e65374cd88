package ai.pricefox.mallfox.model.param;

import ai.pricefox.mallfox.vo.base.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 商品搜索请求
 * @since 2025/7/8
 */
@Data
@Schema(description = "商品搜索与筛选请求")
public class ProductSearchRequest extends PageParam {

    @Schema(description = "搜索关键词")
    private String keyword;

    @Schema(description = "排序规则", defaultValue = "relevance", examples = {"relevance", "price_asc", "price_desc", "rating_desc"})
    private String sortBy = "relevance";

    @Schema(description = "价格区间 - 最低价")
    private Double minPrice;

    @Schema(description = "价格区间 - 最高价")
    private Double maxPrice;

    @Schema(description = "最低顾客评分")
    private Double minRating;

    @Schema(description = "品牌ID列表")
    private List<Long> brandIds;

    @Schema(description = "最小屏幕尺寸 (英寸)")
    private Double minScreenSize;

    @Schema(description = "最大屏幕尺寸 (英寸)")
    private Double maxScreenSize;

    @Schema(description = "最小电池容量 (mAh)")
    private Integer minBatteryCapacity;

    @Schema(description = "最大电池容量 (mAh)")
    private Integer maxBatteryCapacity;

    @Schema(description = "内存大小筛选 (GB) - 固定值列表，如: [4, 6, 8, 12]")
    private List<Integer> ramSizes;

    @Schema(description = "型号年份筛选")
    private List<Integer> modelYears;

    @Schema(description = "颜色筛选")
    private List<String> colors;

    @Schema(description = "存储容量筛选 (具体值，如: 64GB, 128GB, 256GB)")
    private List<Integer> storageCapacities;
}