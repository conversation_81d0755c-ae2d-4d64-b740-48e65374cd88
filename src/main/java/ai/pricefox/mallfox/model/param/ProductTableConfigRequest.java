package ai.pricefox.mallfox.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 产品表格配置请求DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "产品表格配置请求")
public class ProductTableConfigRequest {

    @Schema(description = "主键ID")
    private Integer id;

    @Schema(description = "字段")
    private String field;

    @NotNull(message = "类型不能为空")
    @Schema(description = "类型：1-字段 2-配置", required = true)
    private Integer type;

    @Schema(description = "JSON信息")
    private String info;

    @Schema(description = "权重")
    private Integer weight;


    /**
     * 根据字段更新请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "根据字段更新请求")
    public static class UpdateByFieldRequest {

//        @Schema(description = "主键ID")
//        private Integer id;

        @Schema(description = "字段", required = true)
        private String field;

        @Schema(description = "类型：1-字段 2-配置")
        private Integer type;

        @Schema(description = "JSON信息")
        private String info;

        @Schema(description = "权重")
        private Integer weight;
    }

    /**
     * 根据ID更新权重请求
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "根据ID更新权重请求")
    public static class UpdateWeightRequest {

        @Schema(description = "表格标识")
        private String tag;

        @Schema(description = "主键ID")
        private Integer id;

        @Schema(description = "目标ID")
        private Integer targetId;
    }
}
