package ai.pricefox.mallfox.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * BestBuy API 配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bestbuy.api")
public class BestBuyApiConfig {

    /**
     * BestBuy API 密钥
     */
    private String key = "";

    /**
     * BestBuy API 基础 URL
     */
    private String baseUrl = "https://api.bestbuy.com/v1";

    /**
     * 请求超时时间（毫秒）
     */
    private int timeout = 30000;

    /**
     * 重试次数
     */
    private int maxRetries = 3;

    /**
     * 重试间隔（毫秒）
     */
    private int retryDelay = 1000;

    /**
     * 是否启用模拟模式
     */
    private boolean mockMode = false;

    /**
     * 获取产品详情的端点路径
     */
    public String getProductDetailEndpoint(String sku) {
        try {
            String encodedSku = URLEncoder.encode(sku, StandardCharsets.UTF_8);
            return String.format("/products(sku=%s)", encodedSku);
        } catch (Exception e) {
            // 如果编码失败，使用原始值
            return String.format("/products(sku=%s)", sku);
        }
    }

    /**
     * 获取产品搜索的端点路径
     */
    public String getProductSearchEndpoint(String query) {
        try {
            String encodedQuery = URLEncoder.encode(query, StandardCharsets.UTF_8);
            return String.format("/products(search=%s)", encodedQuery);
        } catch (Exception e) {
            // 如果编码失败，使用原始值
            return String.format("/products(search=%s)", query);
        }
    }

    /**
     * 构建完整的API URL
     */
    public String buildApiUrl(String endpoint) {
        return baseUrl + endpoint + "?apiKey=" + key + "&format=json";
    }

    /**
     * 构建产品详情API URL（包含show参数）
     */
    public String buildProductDetailApiUrl(String endpoint) {
        // 使用show=all来获取所有可用字段，包括颜色、数量、UPC等详细参数
        return baseUrl + endpoint + "?apiKey=" + key + "&format=json&show=all";
    }

    /**
     * 构建产品搜索API URL（包含show参数）
     */
    public String buildProductSearchApiUrl(String endpoint) {
        // 使用show=all来获取所有可用字段，确保搜索结果包含完整信息
        return baseUrl + endpoint + "?apiKey=" + key + "&format=json&show=all";
    }

    /**
     * 检查API密钥是否已配置
     */
    public boolean isApiKeyConfigured() {
        return key != null && !key.trim().isEmpty();
    }
}
