<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.TripartitePlatformMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.TripartitePlatform">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="platformCode" column="platform_code" />
            <result property="platformName" column="platform_name" />
            <result property="sourceType" column="source_type" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,platform_code,
        platform_name,source_type
    </sql>
</mapper>
