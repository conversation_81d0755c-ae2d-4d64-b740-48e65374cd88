<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardColorMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardColor">
            <id property="id" column="id" />
            <result property="createDate" column="create_date" />
            <result property="updateDate" column="update_date" />
            <result property="createUsername" column="create_username" />
            <result property="updateUsername" column="update_username" />
            <result property="codeCodeLevel1" column="code_code_level1" />
            <result property="codeCodeLevel2" column="code_code_level2" />
            <result property="codeNameLevel1" column="code_name_level1" />
            <result property="codeNameLevel2" column="code_name_level2" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,update_date,create_username,update_username,code_code_level1,
        code_code_level2,code_name_level1,code_name_level2
    </sql>
</mapper>
