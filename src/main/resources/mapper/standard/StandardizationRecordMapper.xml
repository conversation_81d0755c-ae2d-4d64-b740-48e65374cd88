<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.StandardizationRecordMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.StandardizationRecord">
        <id property="id" column="id" />
        <result property="createDate" column="create_date"/>
        <result property="createUsername" column="create_username" />
        <result property="updateDate" column="update_date"/>
        <result property="updateUsername" column="update_username" />
        <result property="platformCode" column="platform_code" />
        <result property="dataId" column="data_id" />
        <result property="skuId" column="sku_id" />
        <result property="stepType" column="step_type" />
        <result property="beforeData" column="before_data" />
        <result property="afterData" column="after_data" />
        <result property="changeDetails" column="change_details" />
        <result property="operator" column="operator" />
    </resultMap>

    <sql id="Base_Column_List">
        id,create_date,create_username,update_date,update_username,platform_code,data_id,sku_id,step_type,before_data,after_data,change_details,operator
    </sql>
</mapper>
