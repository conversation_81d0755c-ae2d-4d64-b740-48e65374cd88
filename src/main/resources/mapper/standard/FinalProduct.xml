<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.standard.FinalProductMapper">

    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.standard.FinalProduct">
        <id property="id" column="id" />
        <result property="skuId" column="sku_id" />
        <result property="standardData" column="standard_data" />
        <result property="status" column="status" />
        <result property="reviewTime" column="review_time" />
        <result property="reviewer" column="reviewer" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <sql id="Base_Column_List">
        id,sku_id,standard_data,status,review_time,reviewer,create_time,update_time
    </sql>
</mapper>
