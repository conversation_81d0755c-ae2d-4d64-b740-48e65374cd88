<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.system.ProductTableConfigMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="ai.pricefox.mallfox.domain.product.ProductTableConfig">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="field" property="field" jdbcType="VARCHAR"/>
        <result column="type" property="type" jdbcType="INTEGER"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
        <result column="weight" property="weight" jdbcType="VARCHAR"/>
        <result column="tag" property="tag" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, field, type, info, weight, tag, create_time
    </sql>

    <!-- 根据类型查询配置列表 -->
    <select id="selectByType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM product_table_config
        WHERE type = #{type}
        ORDER BY id DESC
    </select>

    <!-- 根据字段更新配置 -->
    <update id="updateByField">
        UPDATE product_table_config
        <set>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="info != null">
                info = #{info},
            </if>
            <if test="weight != null">
                weight = #{weight},
            </if>
        </set>
        WHERE field = #{field}
    </update>

    <!-- 根据字段查询配置 -->
    <select id="selectByField" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM product_table_config
        WHERE field = #{field}
        LIMIT 1
    </select>

    <!-- 根据字段和类型查询配置 -->
    <select id="selectByFieldAndType" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM product_table_config
        WHERE field = #{field} AND type = #{type}
        LIMIT 1
    </select>

    <!-- 批量插入配置 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO product_table_config (field, type, info, create_time,weight,tag)
        VALUES
        <foreach collection="configs" item="config" separator=",">
            (#{config.field}, #{config.type}, #{config.info}, #{config.createTime},#{config.weight},#{config.weight.tag})
        </foreach>
    </insert>

    <!-- 根据类型查询配置列表并排序 -->
    <select id="selectByTypeWithSort" resultMap="BaseResultMap">
        SELECT *
        FROM product_table_config
        WHERE type = #{type}
        AND tag = #{tag}
        ORDER BY weight ASC
    </select>

    <!-- 查询最大权重值 -->
    <select id="selectMaxWeightByTag" resultType="string">
        SELECT weight
        FROM product_table_config
        WHERE tag = #{tag}
        ORDER BY weight DESC
            LIMIT 1
    </select>

    <!-- 查询最小权重值 -->
    <select id="selectFirstWeightByTag" resultType="string">
        SELECT weight
        FROM product_table_config
        WHERE tag = #{tag}
        ORDER BY weight ASC
            LIMIT 1
    </select>

    <!-- 查询目标的上一条记录的weight值 -->
    <select id="selectPrevWeightByTag" resultType="string">
        SELECT weight
        FROM product_table_config
        <where>
            tag = #{tag}
            AND weight &lt; #{currentWeight} COLLATE utf8mb4_bin
        </where>
        ORDER BY weight COLLATE utf8mb4_bin DESC
        LIMIT 1
    </select>

    <!-- 根据ID查询配置 -->
    <select id="selectById" resultType="string">
        SELECT weight
        FROM product_table_config
        WHERE id = #{id}
    </select>

    <!--  更新权重  -->
    <update id="updateWeightById">
        UPDATE product_table_config
        SET weight = #{weight}
        WHERE id = #{id}
    </update>
</mapper>
