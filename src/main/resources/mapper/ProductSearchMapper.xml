<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductSearchMapper">

    <!-- 商品搜索主查询 - 基于业务表 -->
    <select id="searchProducts" resultType="ai.pricefox.mallfox.model.response.ProductSearchItemDTO">
        SELECT DISTINCT
        pi.spu_id,
        ps.sku_id,
        pi.name as title,
        bi.name as brand,
        psm.model,
        pi.main_image_url as mainImageUrl,
        ps.lowest_price as price,
        ps.highest_price as listPrice,
        CASE
        WHEN ps.highest_price > 0 THEN ROUND((ps.highest_price - ps.lowest_price) / ps.highest_price * 100, 2)
        ELSE 0
        END as discount,
        ps.average_rating as rating,
        ps.total_reviews as reviewCount,
        JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) as color,
        JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')) as storage,
        psm.screen_size_inch as screenSize,
        psm.ram_size_gb as ramMemory,
        psm.battery_capacity_mah as batteryCapacity,
        JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.operating_system')) as operatingSystem,
        JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.processor')) as processor,
        CASE WHEN ps.stock_quantity > 0 THEN 'In Stock' ELSE 'Out of Stock' END as inventory,
        ps.sales_last_30_days as salesLast30Days,
        ps.primary_installment_info as installmentInfo,
        '' as itemUrl,
        '' as seller,
        0 as merchantRating
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
            <if test="request.minPrice != null">
                AND ps.lowest_price >= #{request.minPrice}
            </if>
            <if test="request.maxPrice != null">
                AND ps.lowest_price &lt;= #{request.maxPrice}
            </if>
            <if test="request.minRating != null">
                AND ps.average_rating >= #{request.minRating}
            </if>
            <if test="request.brandIds != null and request.brandIds.size() > 0">
                AND pi.brand_id IN
                <foreach collection="request.brandIds" item="brandId" open="(" separator="," close=")">
                    #{brandId}
                </foreach>
            </if>
            <!-- 屏幕尺寸区间筛选 -->
            <if test="request.minScreenSize != null">
                AND psm.screen_size_inch >= #{request.minScreenSize}
            </if>
            <if test="request.maxScreenSize != null">
                AND psm.screen_size_inch &lt;= #{request.maxScreenSize}
            </if>
            <!-- 电池容量区间筛选 -->
            <if test="request.minBatteryCapacity != null">
                AND psm.battery_capacity_mah >= #{request.minBatteryCapacity}
            </if>
            <if test="request.maxBatteryCapacity != null">
                AND psm.battery_capacity_mah &lt;= #{request.maxBatteryCapacity}
            </if>
            <!-- 内存大小固定值筛选 -->
            <if test="request.ramSizes != null and request.ramSizes.size() > 0">
                AND psm.ram_size_gb IN
                <foreach collection="request.ramSizes" item="ramSize" open="(" separator="," close=")">
                    #{ramSize}
                </foreach>
            </if>
            <!-- 型号年份筛选 -->
            <if test="request.modelYears != null and request.modelYears.size() > 0">
                AND psm.model_year IN
                <foreach collection="request.modelYears" item="year" open="(" separator="," close=")">
                    #{year}
                </foreach>
            </if>
            <!-- 颜色筛选 -->
            <if test="request.colors != null and request.colors.size() > 0">
                AND JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) IN
                <foreach collection="request.colors" item="color" open="(" separator="," close=")">
                    #{color}
                </foreach>
            </if>
            <!-- 存储容量筛选 -->
            <if test="request.storageCapacities != null and request.storageCapacities.size() > 0">
                AND psm.storage_gb IN
                <foreach collection="request.storageCapacities" item="storage" open="(" separator="," close=")">
                    #{storage}
                </foreach>
            </if>
        </where>
        GROUP BY pi.spu_id, ps.sku_id
        <choose>
            <when test="request.sortBy == 'price_asc'">
                ORDER BY ps.lowest_price ASC
            </when>
            <when test="request.sortBy == 'price_desc'">
                ORDER BY ps.lowest_price DESC
            </when>
            <when test="request.sortBy == 'rating_desc'">
                ORDER BY ps.average_rating DESC, ps.total_reviews DESC
            </when>
            <when test="request.sortBy == 'newest'">
                ORDER BY pi.create_time DESC
            </when>
            <otherwise>
                <!-- 默认相关性排序 -->
                ORDER BY
                CASE
                WHEN pi.name LIKE CONCAT('%', COALESCE(#{request.keyword}, ''), '%') THEN 1
                WHEN bi.name LIKE CONCAT('%', COALESCE(#{request.keyword}, ''), '%') THEN 2
                WHEN psm.model LIKE CONCAT('%', COALESCE(#{request.keyword}, ''), '%') THEN 3
                ELSE 4
                END,
                ps.average_rating DESC,
                ps.total_reviews DESC
            </otherwise>
        </choose>
    </select>

    <!-- 获取可用品牌选项 -->
    <select id="getAvailableBrands" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            bi.id as value,
            bi.name as label,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1 AND bi.name IS NOT NULL AND bi.name != ''
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY bi.id, bi.name
        HAVING count > 0
        ORDER BY count DESC, bi.name ASC
    </select>

    <!-- 获取价格统计信息 -->
    <select id="getPriceStatistics" resultType="map">
        SELECT
            MIN(ps.lowest_price) as minPrice,
            MAX(ps.highest_price) as maxPrice,
            AVG(ps.lowest_price) as avgPrice,
            COUNT(DISTINCT pi.spu_id) as productCount
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1 AND ps.lowest_price IS NOT NULL AND ps.lowest_price > 0
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 获取评分分布 -->
    <select id="getRatingDistribution" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(FLOOR(ps.average_rating), ' Stars &amp; Up') as label,
            CONCAT(FLOOR(ps.average_rating)) as value,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1 AND ps.average_rating IS NOT NULL AND ps.average_rating > 0
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY FLOOR(ps.average_rating)
        HAVING count > 0
        ORDER BY FLOOR(ps.average_rating) DESC
    </select>

    <!-- 获取可用存储容量选项 -->
    <select id="getAvailableStorageOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')) as value,
            JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')) as label,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1
            AND JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')) IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')) != ''
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage'))
        HAVING count > 0
        ORDER BY
            CASE
                WHEN JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')) REGEXP '^[0-9]+'
                THEN CAST(SUBSTRING_INDEX(JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.storage')), 'GB', 1) AS UNSIGNED)
                ELSE 999999
            END ASC
    </select>

    <!-- 获取可用颜色选项 -->
    <select id="getAvailableColorOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) as value,
            JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) as label,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1
            AND JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) IS NOT NULL
            AND JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) != ''
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color'))
        HAVING count > 0
        ORDER BY count DESC, JSON_UNQUOTE(JSON_EXTRACT(ps.attributes, '$.color')) ASC
    </select>

    <!-- 获取可用屏幕尺寸选项 -->
    <select id="getAvailableScreenSizeOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(psm.screen_size_inch, ' inches') as value,
            CONCAT(psm.screen_size_inch, ' inches') as label,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1 AND psm.screen_size_inch IS NOT NULL
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY psm.screen_size_inch
        HAVING count > 0
        ORDER BY psm.screen_size_inch ASC
    </select>

    <!-- 获取可用RAM选项 -->
    <select id="getAvailableRamOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(psm.ram_size_gb, 'GB') as value,
            CONCAT(psm.ram_size_gb, 'GB') as label,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1 AND psm.ram_size_gb IS NOT NULL
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY psm.ram_size_gb
        HAVING count > 0
        ORDER BY psm.ram_size_gb ASC
    </select>

    <!-- 获取可用电池容量选项 -->
    <select id="getAvailableBatteryOptions" resultType="ai.pricefox.mallfox.model.response.FilterOption">
        SELECT
            CONCAT(psm.battery_capacity_mah, ' mAh') as value,
            CONCAT(psm.battery_capacity_mah, ' mAh') as label,
            COUNT(DISTINCT pi.spu_id) as count
        FROM product_info pi
        INNER JOIN product_sku ps ON pi.spu_id = ps.spu_id
        LEFT JOIN product_spec_mobile psm ON pi.spu_id = psm.spu_id
        LEFT JOIN brand_info bi ON pi.brand_id = bi.id
        <where>
            pi.status = 1 AND ps.status = 1 AND psm.battery_capacity_mah IS NOT NULL
            <if test="request.keyword != null and request.keyword != ''">
                AND (
                    pi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR bi.name LIKE CONCAT('%', #{request.keyword}, '%')
                    OR psm.model LIKE CONCAT('%', #{request.keyword}, '%')
                )
            </if>
        </where>
        GROUP BY psm.battery_capacity_mah
        HAVING count > 0
        ORDER BY psm.battery_capacity_mah ASC
    </select>

</mapper>
