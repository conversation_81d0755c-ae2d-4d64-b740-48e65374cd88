<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="ai.pricefox.mallfox.mapper.product.ProductInfoMapper">

    <select id="findProductCardList" resultType="ai.pricefox.mallfox.model.response.ProductCardDTO">
        SELECT
        ps.sku_id AS skuId,
        pi.name AS title,
        pi.main_image_url AS imageUrl,
        ps.lowest_price AS price,
        ps.highest_price AS listPrice,
        ps.average_rating AS rating,
        ps.primary_installment_info AS installmentInfo
        FROM
        product_sku ps
        JOIN
        product_info pi ON ps.spu_id = pi.spu_id
        <if test="brandName != null and brandName != ''">
            JOIN brand_info bi ON pi.brand_id = bi.id
        </if>
        <where>
            ps.status = 1 AND pi.status = 1
            <if test="brandName != null and brandName != ''">
                AND bi.name = #{brandName}
            </if>
        </where>
        ORDER BY ${orderByClause}
        LIMIT #{limit}
    </select>

</mapper>