# ===========================================
# 开发环境配置
# ===========================================
spring:
  config:
    activate:
      on-profile: dev  # 激活开发环境
  datasource:
    url: *************************************************************************************************************************************** # 开发数据库URL
    username: root  # 开发数据库用户
    password: PassW0rd2025.  # 开发数据库密码
    driver-class-name: com.mysql.jdbc.Driver  # MySQL驱动
  data:
    redis:
      host: *************  # Redis开发主机
      port: 6379  # Redis端口
      password: PassW0rd2025.  # Redis密码
      database: 1  # Redis库索引
      timeout: 10000  # 连接超时(毫秒)
    # MongoDB开发环境配置
    mongodb:
      uri: ******************************************************************************
  mail:
    host: smtp.office365.com  # 邮件服务器
    port: 587  # 邮件端口
    username: <EMAIL>  # 邮件账号
    password: your-password  # 邮件密码
    properties:
      mail:
        smtp:
          ssl:
            enable: true  # 启用SSL

# ===========================================
# 开发环境日志配置
# ===========================================
logging:
  level:
    # SaToken调试
    ai.pricefox.mallfox.config.SaTokenConfigure: DEBUG
    cn.dev33.satoken: DEBUG
    
    # MyBatis调试
    ai.pricefox.mallfox.mapper: DEBUG
    com.baomidou.mybatisplus: DEBUG
    
    # MongoDB调试
    org.springframework.data.mongodb: DEBUG
    org.mongodb.driver: DEBUG
    
    # 请求日志
    ai.pricefox.mallfox.config.RequestLoggingInterceptor: INFO
    ai.pricefox.mallfox.config.ApiLoggingAspect: INFO
    
    # SQL详细日志
    org.apache.ibatis: DEBUG
    java.sql: DEBUG
    java.sql.Statement: DEBUG
    java.sql.PreparedStatement: DEBUG
    java.sql.ResultSet: DEBUG

# ===========================================
# 开发环境应用日志配置
# ===========================================
app:
  logging:
    enable-request-logging: true  # 启用请求日志
    enable-api-logging: true  # 启用API日志
    enable-sql-logging: true  # 启用SQL日志
    log-request-headers: true  # 记录请求头
    log-request-parameters: true  # 记录请求参数
    log-request-body: true  # 记录请求体
    log-response-body: true  # 记录响应体
    max-request-body-length: 1000  # 请求体最大长度
    max-response-body-length: 1000  # 响应体最大长度

# ===========================================
# OAuth配置 (开发环境)
# ===========================================
google:
  client-id: 928277519574-dpb9c1nel2ivd8pl19d90qps55rkdgse.apps.googleusercontent.com  # Google客户端ID
  client-secret: 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  # Google客户端密钥
  redirect-uri: http://49.234.206.92:28888/api/oauth/google/callback  # 开发环境回调地址

# ===========================================
# 其他服务配置 (开发环境)
# ===========================================
amazon:
  api:
    key:  # 开发环境Amazon API Key
    secret:  # 开发环境Amazon API Secret