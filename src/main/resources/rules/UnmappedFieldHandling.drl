// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardField;
import ai.pricefox.mallfox.service.rules.FieldMappingService;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.ArrayList;

// 未映射字段处理规则
// 该规则用于处理没有平台字段映射关系但有值的字段
rule "Handle Unmapped Fields"
    no-loop true
    salience -100  // 降低优先级，确保在其他规则执行后再执行
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $standardFields: List() from collect(StandardField())
    then
        // 获取已映射的字段名集合
        List mappedFields = new ArrayList();
        // 这里应该从其他规则执行结果中获取已映射字段，暂时用空集合作为示例

        // 使用反射获取平台数据对象的所有字段
        Field[] fields = $productData.getClass().getDeclaredFields();

        // 处理没有映射关系但有值的字段
        for (Field field : fields) {
            try {
                String fieldName = field.getName();
                // 如果该字段已经有映射关系，跳过 (忽略大小写比较)
                if (isFieldMapped(mappedFields, fieldName)) {
                    continue;
                }

                // 设置字段可访问
                field.setAccessible(true);

                // 获取字段值
                Object value = field.get($productData);
                if (value != null && !value.toString().trim().isEmpty()) {
                    // 查找标准字段库中是否存在匹配的字段名（忽略大小写）
                    String standardFieldName = findMatchingStandardFieldName(fieldName, $standardFields);
                    if (standardFieldName != null) {
                        $standardProduct.setField(standardFieldName, value.toString());
                        RuleLogUtil.info("映射到标准字段: " + fieldName + " -> " + standardFieldName + " = " + value);
                    } else {
                        // 如果没有找到匹配的标准字段，则使用字段名的标准化形式
                        String normalizedFieldName = normalizeFieldName(fieldName);
                        $standardProduct.setField(normalizedFieldName, value.toString());
                        RuleLogUtil.info("直接映射字段: " + normalizedFieldName + " = " + value);
                    }
                }
            } catch (IllegalAccessException e) {
                RuleLogUtil.warn("无法访问字段: " + field.getName());
            }
        }
end

// 检查字段是否已映射（忽略大小写）
function boolean isFieldMapped(List mappedFields, String fieldName) {
    for (Object mappedField : mappedFields) {
        if (((String)mappedField).equalsIgnoreCase(fieldName)) {
            return true;
        }
    }
    return false;
}

// 在标准字段库中查找匹配的字段名（忽略大小写）
function String findMatchingStandardFieldName(String fieldName, List standardFields) {
    for (Object obj : standardFields) {
        StandardField standardField = (StandardField) obj;
        if (standardField.getFieldNameEn() != null &&
            standardField.getFieldNameEn().equalsIgnoreCase(fieldName)) {
            return standardField.getFieldNameEn();
        }
    }
    return null;
}

// 标准化字段名（首字母大写，其余小写）
// 注意：此函数已在FieldStandardization.drl中定义，此处删除重复定义
