// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardBrand;
import ai.pricefox.mallfox.domain.standard.StandardColor;
import ai.pricefox.mallfox.domain.standard.StandardModel;
import ai.pricefox.mallfox.domain.standard.StandardStorage;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import java.util.List;
import java.util.regex.Pattern;

// 通用字段处理规则
rule "Process Common Fields"
    no-loop true
    salience 70
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
    then
        // 处理标题字段
        if ($productData.getTitle() != null && !$productData.getTitle().trim().isEmpty()) {
            $standardProduct.setField("Title", $productData.getTitle());
            RuleLogUtil.info("Setting Title field: " + $productData.getTitle());
        }

        // 处理UPC字段
        if ($productData.getUpcCode() != null && !$productData.getUpcCode().trim().isEmpty()) {
            $standardProduct.setField("UpcCode", $productData.getUpcCode());
            RuleLogUtil.info("Setting UpcCode field: " + $productData.getUpcCode());
        }

        // 处理商品链接
        if ($productData.getItemUrl() != null && !$productData.getItemUrl().trim().isEmpty()) {
            $standardProduct.setField("ItemUrl", $productData.getItemUrl());
            RuleLogUtil.info("Setting ItemUrl field: " + $productData.getItemUrl());
        }
end

// 品牌标准化处理规则
rule "Process Brand Standardization"
    no-loop true
    salience 50
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $brands: List() from collect(StandardBrand())
    then
        boolean processed = false;

        // 第一步：如果源数据有值，则尝试与标准库匹配
        String brand = $standardProduct.getStringField("Brand");
        if (brand != null && !brand.trim().isEmpty() && !processed) {
            String originalBrand = brand.trim();
            String standardizedBrand = matchBrandWithStandardLibrary(originalBrand, $brands);

            if (standardizedBrand != null) {
                // 匹配成功，使用标准库的值
                $standardProduct.setField("Brand", standardizedBrand);
                RuleLogUtil.info("Brand matched with standard library: '" + originalBrand + "' -> '" + standardizedBrand + "'");
                processed = true;
            }
        }

        // 第二步：如果源数据没有值或未匹配成功，则从标题中提取
        String title = $productData.getTitle();
        if (title != null && !processed) {
            title = title.toLowerCase();

            // 尝试从标准库中匹配标题
            for (Object brandObj : $brands) {
                StandardBrand standardBrand = (StandardBrand) brandObj;
                String brandNameEn = standardBrand.getBrandNameEn();
                String brandNameCn = standardBrand.getBrandNameCn();

                // 检查标题是否包含品牌名称
                if ((brandNameEn != null && Pattern.compile("\\b" + Pattern.quote(brandNameEn.toLowerCase()) + "\\b").matcher(title).find()) ||
                    (brandNameCn != null && title.contains(brandNameCn.toLowerCase()))) {
                    $standardProduct.setField("Brand", standardBrand.getBrandNameEn());
                    RuleLogUtil.info("Brand extracted from title: '" + title + "' -> '" + standardBrand.getBrandNameEn() + "'");
                    processed = true;
                    break;
                }
            }

            // 使用默认匹配规则
            if (!processed && (title.contains("apple") && (title.contains("iphone") || title.contains("ipad") || title.contains("mac")))) {
                $standardProduct.setField("Brand", "Apple");
                RuleLogUtil.info("Default brand extracted from title: Apple");
                processed = true;
            } else if (!processed && title.contains("samsung")) {
                $standardProduct.setField("Brand", "Samsung");
                RuleLogUtil.info("Default brand extracted from title: Samsung");
                processed = true;
            } else if (!processed && title.contains("google")) {
                $standardProduct.setField("Brand", "Google");
                RuleLogUtil.info("Default brand extracted from title: Google");
                processed = true;
            }
        }

        // 第三步：如果以上都未设置值，则使用原始数据中的品牌
        if (!processed && ($standardProduct.getStringField("Brand") == null || $standardProduct.getStringField("Brand").trim().isEmpty())
            && $productData.getBrand() != null && !$productData.getBrand().trim().isEmpty()) {
            $standardProduct.setField("Brand", $productData.getBrand());
            RuleLogUtil.info("Using original brand data: " + $productData.getBrand());
        }
end

// 型号标准化处理规则
rule "Process Model Standardization"
    no-loop true
    salience 50
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $models: List() from collect(StandardModel())
    then
        boolean processed = false;

        // 第一步：如果源数据有值，则尝试与标准库匹配
        String model = $standardProduct.getStringField("Model");
        if (model != null && !model.trim().isEmpty() && !processed) {
            String originalModel = model.trim();
            String standardizedModel = matchModelWithStandardLibrary(originalModel, $models);

            if (standardizedModel != null) {
                // 匹配成功，使用标准库的值
                $standardProduct.setField("Model", standardizedModel);
                RuleLogUtil.info("Model matched with standard library: '" + originalModel + "' -> '" + standardizedModel + "'");
                processed = true;
            }
        }

        // 第二步：如果源数据没有值或未匹配成功，则从标题中提取
        String title = $productData.getTitle();
        if (title != null && !processed) {
            title = title.toLowerCase();

            // 尝试从标准库中匹配标题
            for (Object modelObj : $models) {
                StandardModel standardModel = (StandardModel) modelObj;
                String modelName = standardModel.getModelName();

                if (modelName != null && title.contains(modelName.toLowerCase())) {
                    $standardProduct.setField("Model", standardModel.getModelName());
                    RuleLogUtil.info("Model extracted from title: '" + title + "' -> '" + standardModel.getModelName() + "'");
                    processed = true;
                    break;
                }
            }

            // 使用正则表达式提取型号
            if (!processed) {
                Pattern pattern = Pattern.compile("([A-Za-z]+(?:\\s+[A-Za-z0-9]+)*)");
                java.util.regex.Matcher matcher = pattern.matcher(title);
                String resultModel = null;
                while (matcher.find() && resultModel == null) {
                    String candidate = matcher.group(1);
                    if (candidate.matches(".*\\d+.*")) {  // 包含数字的候选型号
                        resultModel = candidate;
                    }
                }
                if (resultModel == null) {
                    matcher.reset();
                    if (matcher.find()) {
                        resultModel = matcher.group(1);
                    }
                }
                if (resultModel != null) {
                    $standardProduct.setField("Model", resultModel);
                    RuleLogUtil.info("Model extracted from title using regex: " + resultModel);
                    processed = true;
                }
            }
        }

        // 第三步：如果以上都未设置值，则使用原始数据中的型号
        if (!processed && ($standardProduct.getStringField("Model") == null || $standardProduct.getStringField("Model").trim().isEmpty())
            && $productData.getModel() != null && !$productData.getModel().trim().isEmpty()) {
            $standardProduct.setField("Model", $productData.getModel());
            RuleLogUtil.info("Using original model data: " + $productData.getModel());
        }
end

// 颜色标准化处理规则
rule "Process Color Standardization"
    no-loop true
    salience 50
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $colors: List() from collect(StandardColor())
    then
        boolean processed = false;

        // 第一步：如果源数据有值，则尝试与标准库匹配
        String color = $standardProduct.getStringField("Color");
        if (color != null && !color.trim().isEmpty() && !processed) {
            String originalColor = color.trim();
            String standardizedColor = matchColorWithStandardLibrary(originalColor, $colors);

            if (standardizedColor != null) {
                // 匹配成功，使用标准库的值
                $standardProduct.setField("Color", standardizedColor);
                RuleLogUtil.info("Color matched with standard library: '" + originalColor + "' -> '" + standardizedColor + "'");
                processed = true;
            }
        }

        // 第二步：如果源数据没有值或未匹配成功，则从标题中提取
        String title = $productData.getTitle();
        if (title != null && !processed) {
            title = title.toLowerCase();

            // 尝试从标准库中匹配标题
            for (Object colorObj : $colors) {
                StandardColor standardColor = (StandardColor) colorObj;
                String codeNameLevel1 = standardColor.getCodeNameLevel1();
                String codeNameLevel2 = standardColor.getCodeNameLevel2();

                // 检查标题是否包含颜色名称（使用codeNameLevel2进行匹配，忽略大小写和空格）
                if (codeNameLevel2 != null) {
                    String normalizedCodeNameLevel2 = codeNameLevel2.replaceAll("\\s+", "").toLowerCase();
                    String normalizedTitle = title.replaceAll("\\s+", "");
                    if (normalizedTitle.contains(normalizedCodeNameLevel2)) {
                        $standardProduct.setField("Color", codeNameLevel1); // 使用codeNameLevel1作为标准值
                        RuleLogUtil.info("Color extracted from title: '" + title + "' -> '" + codeNameLevel1 + "'");
                        processed = true;
                        break;
                    }
                }
            }

//            // 使用默认匹配规则
//            if (!processed && (title.contains("blue titanium") || title.contains("blue"))) {
//                $standardProduct.setField("Color", "Blue");
//                RuleLogUtil.info("Default color extracted from title: Blue");
//                processed = true;
//            } else if (!processed && (title.contains("black titanium") || title.contains("black"))) {
//                $standardProduct.setField("Color", "Black");
//                RuleLogUtil.info("Default color extracted from title: Black");
//                processed = true;
//            } else if (!processed && (title.contains("white titanium") || title.contains("white"))) {
//                $standardProduct.setField("Color", "White");
//                RuleLogUtil.info("Default color extracted from title: White");
//                processed = true;
//            } else if (!processed && (title.contains("gold titanium") || title.contains("gold"))) {
//                $standardProduct.setField("Color", "Gold");
//                RuleLogUtil.info("Default color extracted from title: Gold");
//                processed = true;
//            }
        }

        // 第三步：如果以上都未设置值，则使用原始数据中的颜色
        if (!processed && ($standardProduct.getStringField("Color") == null || $standardProduct.getStringField("Color").trim().isEmpty())
            && $productData.getColor() != null && !$productData.getColor().trim().isEmpty()) {
            $standardProduct.setField("Color", $productData.getColor());
            RuleLogUtil.info("Using original color data: " + $productData.getColor());
        }
end

// 存储标准化处理规则
rule "Process Storage Standardization"
    no-loop true
    salience 50
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $storages: List() from collect(StandardStorage())
    then
        boolean processed = false;

        // 第一步：如果源数据有值，则尝试与标准库匹配
        String storage = $standardProduct.getStringField("Storage");
        if (storage != null && !storage.trim().isEmpty() && !processed) {
            String originalStorage = storage.trim();
            String standardizedStorage = matchStorageWithStandardLibrary(originalStorage, $storages);

            if (standardizedStorage != null) {
                // 匹配成功，使用标准库的值
                $standardProduct.setField("Storage", standardizedStorage);
                RuleLogUtil.info("Storage matched with standard library: '" + originalStorage + "' -> '" + standardizedStorage + "'");
                processed = true;
            }
        }

        // 第二步：如果源数据没有值或未匹配成功，则从标题中提取
        String title = $productData.getTitle();
        if (title != null && !processed) {
            title = title.toLowerCase();

            // 尝试从标准库中匹配标题
            for (Object storageObj : $storages) {
                StandardStorage standardStorage = (StandardStorage) storageObj;
                String storageName = standardStorage.getStorageName();

                if (storageName != null && title.contains(storageName.toLowerCase())) {
                    $standardProduct.setField("Storage", standardStorage.getStorageName());
                    RuleLogUtil.info("Storage extracted from title: '" + title + "' -> '" + standardStorage.getStorageName() + "'");
                    processed = true;
                    break;
                }
            }

//            // 使用默认匹配规则
//            if (!processed && (title.contains("512gb") || title.contains("512 gb"))) {
//                $standardProduct.setField("Storage", "512GB");
//                RuleLogUtil.info("Default storage extracted from title: 512GB");
//                processed = true;
//            } else if (!processed && (title.contains("256gb") || title.contains("256 gb"))) {
//                $standardProduct.setField("Storage", "256GB");
//                RuleLogUtil.info("Default storage extracted from title: 256GB");
//                processed = true;
//            } else if (!processed && (title.contains("128gb") || title.contains("128 gb"))) {
//                $standardProduct.setField("Storage", "128GB");
//                RuleLogUtil.info("Default storage extracted from title: 128GB");
//                processed = true;
//            } else if (!processed && (title.contains("64gb") || title.contains("64 gb"))) {
//                $standardProduct.setField("Storage", "64GB");
//                RuleLogUtil.info("Default storage extracted from title: 64GB");
//                processed = true;
//            } else if (!processed && (title.contains("1tb") || title.contains("1 tb"))) {
//                $standardProduct.setField("Storage", "1TB");
//                RuleLogUtil.info("Default storage extracted from title: 1TB");
//                processed = true;
//            }
        }

        // 第三步：如果以上都未设置值，则使用原始数据中的存储
        if (!processed && ($standardProduct.getStringField("Storage") == null || $standardProduct.getStringField("Storage").trim().isEmpty())
            && $productData.getStorage() != null && !$productData.getStorage().trim().isEmpty()) {
            $standardProduct.setField("Storage", $productData.getStorage());
            RuleLogUtil.info("Using original storage data: " + $productData.getStorage());
        }
end

// 品类标准化处理规则
rule "Process Category Standardization"
    no-loop true
    salience 50
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $categories: List() from collect(StandardCategory())
    then
        boolean processed = false;

        // 第一步：如果源数据有值，则尝试与标准库匹配
        String category = $standardProduct.getStringField("Category");
        if (category != null && !category.trim().isEmpty() && !processed) {
            String originalCategory = category.trim();
            String standardizedCategory = matchCategoryWithStandardLibrary(originalCategory, $categories);

            if (standardizedCategory != null) {
                // 匹配成功，使用标准库的值
                $standardProduct.setField("Category", standardizedCategory);
                RuleLogUtil.info("Category matched with standard library: '" + originalCategory + "' -> '" + standardizedCategory + "'");
                processed = true;
            }
        }

        // 第二步：如果源数据没有值或未匹配成功，则从标题中提取
        String title = $productData.getTitle();
        if (title != null && !processed) {
            title = title.toLowerCase();

            // 尝试从标准库中匹配标题
            for (Object categoryObj : $categories) {
                StandardCategory standardCategory = (StandardCategory) categoryObj;
                String categoryNameCn = standardCategory.getCategoryNameCn();
                String categoryNameEn = standardCategory.getCategoryNameEn();

                if ((categoryNameCn != null && title.contains(categoryNameCn.toLowerCase())) ||
                    (categoryNameEn != null && title.contains(categoryNameEn.toLowerCase()))) {
                    $standardProduct.setField("Category", categoryNameEn != null ? categoryNameEn : categoryNameCn);
                    RuleLogUtil.info("Category extracted from title: '" + title + "' -> '" + (categoryNameEn != null ? categoryNameEn : categoryNameCn) + "'");
                    processed = true;
                    break;
                }
            }
        }

        // 第三步：如果以上都未设置值，则使用原始数据中的品类
        if (!processed && ($standardProduct.getStringField("Category") == null || $standardProduct.getStringField("Category").trim().isEmpty())
            && $productData.getCategoryLevel1() != null && !$productData.getCategoryLevel1().trim().isEmpty()) {
            $standardProduct.setField("Category", $productData.getCategoryLevel1());
            RuleLogUtil.info("Using original category data: " + $productData.getCategoryLevel1());
        }
end

// 品牌匹配函数
function String matchBrandWithStandardLibrary(String originalValue, List brands) {
    for (Object brandObj : brands) {
        StandardBrand standardBrand = (StandardBrand) brandObj;
        String brandNameEn = standardBrand.getBrandNameEn();
        String brandNameCn = standardBrand.getBrandNameCn();

        if (originalValue.equalsIgnoreCase(brandNameEn) || originalValue.equalsIgnoreCase(brandNameCn)) {
            return standardBrand.getBrandNameEn();
        }
    }
    return null; // 未匹配到
}

// 型号匹配函数
function String matchModelWithStandardLibrary(String originalValue, List models) {
    for (Object modelObj : models) {
        StandardModel standardModel = (StandardModel) modelObj;
        String modelName = standardModel.getModelName();

        if (originalValue.equalsIgnoreCase(modelName)) {
            return standardModel.getModelName();
        }
    }
    return null; // 未匹配到
}

// 颜色匹配函数
function String matchColorWithStandardLibrary(String originalValue, List colors) {
    for (Object colorObj : colors) {
        StandardColor standardColor = (StandardColor) colorObj;
        String codeNameLevel2 = standardColor.getCodeNameLevel2();

        // 移除空格并转换为小写进行比较
        if (codeNameLevel2 != null) {
            String normalizedCodeNameLevel2 = codeNameLevel2.replaceAll("\\s+", "").toLowerCase();
            String normalizedOriginalValue = originalValue.replaceAll("\\s+", "").toLowerCase();

            if (normalizedCodeNameLevel2.equals(normalizedOriginalValue)) {
                return standardColor.getCodeNameLevel1(); // 使用codeNameLevel1作为标准值
            }
        }
    }
    return null; // 未匹配到
}

// 存储匹配函数
function String matchStorageWithStandardLibrary(String originalValue, List storages) {
    for (Object storageObj : storages) {
        StandardStorage standardStorage = (StandardStorage) storageObj;
        String storageName = standardStorage.getStorageName();

        if (originalValue.equalsIgnoreCase(storageName)) {
            return standardStorage.getStorageName();
        }
    }
    return null; // 未匹配到
}

// 品类匹配函数
function String matchCategoryWithStandardLibrary(String originalValue, List categories) {
    for (Object categoryObj : categories) {
        StandardCategory standardCategory = (StandardCategory) categoryObj;
        String categoryNameCn = standardCategory.getCategoryNameCn();
        String categoryNameEn = standardCategory.getCategoryNameEn();

        if (originalValue.equalsIgnoreCase(categoryNameCn) || originalValue.equalsIgnoreCase(categoryNameEn)) {
            return categoryNameEn != null ? categoryNameEn : categoryNameCn;
        }
    }
    return null; // 未匹配到
}
