// 定义规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.PlatformCategoryMapping;

/**
 * 平台品类映射规则
 * 该规则负责将不同电商平台的品类信息映射到统一的标准品类编码体系中
 */
rule "Platform Category Mapping"
    // no-loop true 表示该规则执行后不会重新激活自己，防止无限循环
    no-loop true
    // when部分定义规则的条件（LHS - Left Hand Side）
    when
        // 匹配ProductDataDTO对象，要求sourcePlatform字段不为null
        // 并将匹配到的对象命名为$productData
        $productData: ProductDataDTO(sourcePlatform != null)

        // 匹配DynamicStandardProduct对象（标准产品对象）
        // 并将匹配到的对象命名为$standardProduct
        $standardProduct: DynamicStandardProduct()

        // 匹配PlatformCategoryMapping对象，要求platformCode字段等于$productData.getSourcePlatform()的返回值
        // 并将匹配到的对象命名为$categoryMapping
        $categoryMapping: PlatformCategoryMapping(platformCode == $productData.getSourcePlatform())

    // then部分定义满足条件时要执行的动作（RHS - Right Hand Side）
    then
        // 调用DynamicStandardProduct的setField方法，设置"Category"字段的值为$categoryMapping.getStandardCategoryCode()
        // 将平台品类映射到标准品类编码
        $standardProduct.setField("Category", $categoryMapping.getStandardCategoryCode());

        // 打印日志信息，显示从哪个平台映射到了哪个标准品类
       RuleLogUtil.info("Mapped category from platform " + $productData.getSourcePlatform() +
                          " to standard category: " + $categoryMapping.getStandardCategoryCode());
// 规则结束
end
