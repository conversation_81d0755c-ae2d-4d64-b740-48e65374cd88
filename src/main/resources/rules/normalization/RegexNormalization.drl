// 文件: src/main/resources/rules/normalization/RegexNormalization.drl
package rules.normalization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.NormalizationLibrary;

// 使用正则表达式进行归一化
rule "Regex Based Normalization"
    no-loop true
    when
        $standardProduct: DynamicStandardProduct()
        $normLib: NormalizationLibrary(originalValue != null, originalValue matches ".*[*+?{}()|\\[\\]\\\\].*")
    then
        try {
            String pattern = $normLib.getOriginalValue();
            String replacement = $normLib.getNormalizedValue();

            if ($standardProduct.hasField("Color")) {
                String originalColor = $standardProduct.getStringField("Color");
                String normalizedColor = originalColor.replaceAll(pattern, replacement);
                
                if (!normalizedColor.equals(originalColor)) {
                    $standardProduct.setField("Color", normalizedColor);
                    RuleLogUtil.info("Regex normalized color: " + originalColor + " -> " + normalizedColor);
                }
            }
            
            if ($standardProduct.hasField("Storage")) {
                String originalStorage = $standardProduct.getStringField("Storage");
                String normalizedStorage = originalStorage.replaceAll(pattern, replacement);
                
                if (!normalizedStorage.equals(originalStorage)) {
                    $standardProduct.setField("Storage", normalizedStorage);
                    RuleLogUtil.info("Regex normalized storage: " + originalStorage + " -> " + normalizedStorage);
                }
            }
        } catch (Exception e) {
            RuleLogUtil.warn("Error in regex normalization: " + e.getMessage());
        }
end
