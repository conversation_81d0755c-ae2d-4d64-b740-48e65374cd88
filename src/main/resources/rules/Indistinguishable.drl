// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.NormalizationRules;
import ai.pricefox.mallfox.enums.ApplyToEnum;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

// 无差异逻辑规则
// 该规则用于处理数据中的无差异项，即不同写法但含义相同的值
rule "Apply Indistinguishable Rules From Normalization Rules"
    // no-loop true 表示该规则执行后不会重新激活自己，防止无限循环
    no-loop true
    // when部分定义规则的条件（LHS - Left Hand Side）
    when
        // 匹配ProductDataDTO对象（源数据对象）
        $productData: ProductDataDTO()
        // 匹配DynamicStandardProduct对象（标准化产品对象）
        $standardProduct: DynamicStandardProduct()
        // 收集所有的NormalizationRules对象到列表中
        $normRules: List() from collect(NormalizationRules())
    // then部分定义满足条件时要执行的动作（RHS - Right Hand Side）
    then
        // 遍历所有无差异规则
        for (Object ruleObj : $normRules) {
            // 类型转换为NormalizationRules对象
            NormalizationRules normRule = (NormalizationRules) ruleObj;
            // 获取正则表达式模式和替换字符串
            String patternStr = normRule.getPattern();
            String replacement = normRule.getReplacement();
            // 获取应用范围（全部字段、品牌、颜色等）
            ApplyToEnum applyTo = normRule.getApplyTo();

            // 检查规则的必要字段是否都存在
            if (patternStr != null && replacement != null && applyTo != null) {
                try {
                    // 编译正则表达式模式
                    Pattern pattern = Pattern.compile(patternStr);

                    // 根据apply_to确定应用范围
                    switch (applyTo) {
                        case ALL:
                            // 对所有字段应用规则
                            Map<String, Object> fields = $standardProduct.getFields();
                            Set<String> fieldNames = fields.keySet();
                            // 遍历所有字段
                            for (String fieldName : fieldNames) {
                                // 检查字段是否存在且不为空
                                if ($standardProduct.hasField(fieldName) && $standardProduct.getStringField(fieldName) != null) {
                                    // 获取字段值
                                    String fieldValue = $standardProduct.getStringField(fieldName);
                                    // 使用正则表达式替换匹配的内容
                                    String normalizedValue = pattern.matcher(fieldValue).replaceAll(replacement);
                                    // 如果替换后的内容与原内容不同，则更新字段值
                                    if (!normalizedValue.equals(fieldValue)) {
                                        $standardProduct.setField(fieldName, normalizedValue);
                                       RuleLogUtil.info("Applied indistinguishable rule to " + fieldName + ": '" + fieldValue + "' -> '" + normalizedValue + "'");
                                    }
                                }
                            }
                            break;
                        case BRAND:
                            // 仅对品牌字段应用规则
                            if ($standardProduct.hasField("Brand") && $standardProduct.getStringField("Brand") != null) {
                                String fieldValue1 = $standardProduct.getStringField("Brand");
                                String normalizedValue1 = pattern.matcher(fieldValue1).replaceAll(replacement);
                                if (!normalizedValue1.equals(fieldValue1)) {
                                    $standardProduct.setField("Brand", normalizedValue1);
                                   RuleLogUtil.info("Applied indistinguishable rule to Brand: '" + fieldValue1 + "' -> '" + normalizedValue1+ "'");
                                }
                            }
                            break;
                        case COLOR:
                            // 仅对颜色字段应用规则
                            if ($standardProduct.hasField("Color") && $standardProduct.getStringField("Color") != null) {
                                String fieldValue2 = $standardProduct.getStringField("Color");
                                String normalizedValue2 = pattern.matcher(fieldValue2).replaceAll(replacement);
                                if (!normalizedValue2.equals(fieldValue2)) {
                                    $standardProduct.setField("Color", normalizedValue2);
                                   RuleLogUtil.info("Applied indistinguishable rule to Color: '" + fieldValue2 + "' -> '" + normalizedValue2 + "'" + "' -> '" + normalizedValue2 + "'");
                                }
                            }
                            break;
                        case STORAGE:
                            // 仅对存储字段应用规则
                            if ($standardProduct.hasField("Storage") && $standardProduct.getStringField("Storage") != null) {
                                String fieldValue3 = $standardProduct.getStringField("Storage");
                                String normalizedValue3 = pattern.matcher(fieldValue3).replaceAll(replacement);
                                if (!normalizedValue3.equals(fieldValue3)) {
                                    // 注意：这里应该使用normalizedValue3而不是normalizedValue
                                    $standardProduct.setField("Storage", normalizedValue3);
                                   RuleLogUtil.info("Applied indistinguishable rule to Storage: '" + fieldValue3 + "' -> '" + normalizedValue3 + "'");
                                }
                            }
                            break;
                        case MODEL:
                            // 仅对型号字段应用规则
                            if ($standardProduct.hasField("Model") && $standardProduct.getStringField("Model") != null) {
                                String fieldValue4 = $standardProduct.getStringField("Model");
                                String normalizedValue4 = pattern.matcher(fieldValue4).replaceAll(replacement);
                                if (!normalizedValue4.equals(fieldValue4)) {
                                    $standardProduct.setField("Model", normalizedValue4);
                                   RuleLogUtil.info("Applied indistinguishable rule to Model: '" + fieldValue4 + "' -> '" + normalizedValue4 + "'");
                                }
                            }
                            break;
                        case CATEGORY:
                            // 仅对品类字段应用规则
                            if ($standardProduct.hasField("Category") && $standardProduct.getStringField("Category") != null) {
                                String fieldValue5 = $standardProduct.getStringField("Category");
                                String normalizedValue5 = pattern.matcher(fieldValue5).replaceAll(replacement);
                                if (!normalizedValue5.equals(fieldValue5)) {
                                    $standardProduct.setField("Category", normalizedValue5);
                                   RuleLogUtil.info("Applied indistinguishable rule to Category: '" + fieldValue5 + "' -> '" + normalizedValue5 + "'");
                                }
                            }
                            break;
                        case TITLE:
                            // 仅对标题字段应用规则
                            if ($standardProduct.hasField("Title") && $standardProduct.getStringField("Title") != null) {
                                String fieldValue6 = $standardProduct.getStringField("Title");
                                String normalizedValue6 = pattern.matcher(fieldValue6).replaceAll(replacement);
                                if (!normalizedValue6.equals(fieldValue6)) {
                                    $standardProduct.setField("Title", normalizedValue6);
                                   RuleLogUtil.info("Applied indistinguishable rule to Title: '" + fieldValue6 + "' -> '" + normalizedValue6 + "'");
                                }
                            }
                            break;
                    }
                } catch (Exception e) {
                    // 捕获并打印规则应用中的异常，包括错误类型
                    System.err.println("Error applying indistinguishable rule: " + 
                                      "pattern='" + patternStr + "', " + 
                                      "replacement='" + replacement + "', " + 
                                      "errorType=" + e.getClass().getName() + ", " +
                                      "errorMessage=" + e.getMessage());
                    
                    // 特别处理非法字符范围错误
                    if (e.getMessage().contains("Illegal character range")) {
                        System.err.println("提示: 正则表达式 '" + patternStr + "' 包含非法字符范围。" +
                                         "如果要匹配连字符 '-'，请将其放在字符类的开头或结尾，例如: '[\\s_-]' 或 '[_\\s-]'");
                    }
                    
                    // 打印异常堆栈跟踪
                    e.printStackTrace();
                }
            }
        }
end
