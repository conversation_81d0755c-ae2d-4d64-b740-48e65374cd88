// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import java.lang.reflect.Field;

// 平台字段映射规则
// 该规则用于将平台特定字段映射到标准字段
rule "Platform Field Mapping"
    no-loop true
    when
        $productData: ProductDataDTO()
        $standardProduct: DynamicStandardProduct()
        $platformFieldMapping: PlatformFieldMapping(platformCode == $productData.getPlatformCode())
        $standardField: StandardField(fieldCode == $platformFieldMapping.getStandardFieldCode())
    then
        String sourceFieldName = $platformFieldMapping.getSourceFieldName();
        String standardFieldName = $standardField.getFieldNameEn();

        // 使用反射获取源字段值
        try {
            Field sourceField = findFieldIgnoreCase($productData.getClass(), sourceFieldName);
            if (sourceField != null) {
                sourceField.setAccessible(true);
                Object value = sourceField.get($productData);
                if (value != null && !value.toString().trim().isEmpty()) {
                    // 使用标准字段名设置值
                    $standardProduct.setField(standardFieldName, value.toString());
                    RuleLogUtil.info("映射字段: " + sourceFieldName + " -> " + standardFieldName + " = " + value);
                }
            }
        } catch (Exception e) {
            RuleLogUtil.warn("映射字段 " + sourceFieldName + " 时出错: " + e.getMessage());
        }
end

// 查找字段（忽略大小写）
function Field findFieldIgnoreCase(Class clazz, String fieldName) {
    try {
        // 首先尝试精确匹配
        return clazz.getDeclaredField(fieldName);
    } catch (Exception e) {
        // 如果精确匹配失败，尝试大小写不敏感匹配
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (field.getName().equalsIgnoreCase(fieldName)) {
                return field;
            }
        }
        return null;
    }
}
