// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.InvalidValuePatterns;
import java.util.List;
import java.util.regex.Pattern;

// 无效字段库规则
// 该规则用于识别和处理无效的字段值
rule "Handle Invalid Values Using Patterns"
    // no-loop true 表示该规则执行后不会重新激活自己，防止无限循环
    no-loop true
    // when部分定义规则的条件（LHS - Left Hand Side）
    when
        // 匹配ProductDataDTO对象（源数据对象）
        $productData: ProductDataDTO()
        // 匹配DynamicStandardProduct对象（标准化产品对象）
        $standardProduct: DynamicStandardProduct()
        // 收集所有的InvalidValuePatterns对象到列表中
        $invalidPatterns: List() from collect(InvalidValuePatterns())
    // then部分定义满足条件时要执行的动作（RHS - Right Hand Side）
    then
        // 遍历所有无效值模式
        for (Object patternObj : $invalidPatterns) {
            // 类型转换为InvalidValuePatterns对象
            InvalidValuePatterns invalidPattern = (InvalidValuePatterns) patternObj;
            // 获取正则表达式模式
            String patternStr = invalidPattern.getPattern();

            // 检查模式是否为空
            if (patternStr != null) {
                try {
                    // 编译正则表达式模式（忽略大小写）
                    Pattern pattern = Pattern.compile(patternStr, Pattern.CASE_INSENSITIVE);

                    // 检查品牌是否为无效值
                    if ($standardProduct.hasField("Brand") && $standardProduct.getStringField("Brand") != null) {
                        // 获取品牌字段值
                        String brandValue = $standardProduct.getStringField("Brand");
                        // 检查是否匹配无效值模式
                        if (pattern.matcher(brandValue).matches()) {
                            // 如果匹配，设置为默认的无效品牌值
                            $standardProduct.setField("Brand", "Unknown Brand");
                           RuleLogUtil.info("Set brand to 'Unknown Brand' due to invalid pattern: " + patternStr);
                        }
                    }

                    // 检查颜色是否为无效值
                    if ($standardProduct.hasField("Color") && $standardProduct.getStringField("Color") != null) {
                        // 获取颜色字段值
                        String colorValue = $standardProduct.getStringField("Color");
                        // 检查是否匹配无效值模式
                        if (pattern.matcher(colorValue).matches()) {
                            // 如果匹配，设置为默认的无效颜色值
                            $standardProduct.setField("Color", "Unknown Color");
                           RuleLogUtil.info("Set color to 'Unknown Color' due to invalid pattern: " + patternStr);
                        }
                    }

                    // 检查存储是否为无效值
                    if ($standardProduct.hasField("Storage") && $standardProduct.getStringField("Storage") != null) {
                        // 获取存储字段值
                        String storageValue = $standardProduct.getStringField("Storage");
                        // 检查是否匹配无效值模式
                        if (pattern.matcher(storageValue).matches()) {
                            // 如果匹配，设置为默认的无效存储值
                            $standardProduct.setField("Storage", "Unknown Storage");
                           RuleLogUtil.info("Set storage to 'Unknown Storage' due to invalid pattern: " + patternStr);
                        }
                    }

                    // 检查型号是否为无效值
                    if ($standardProduct.hasField("Model") && $standardProduct.getStringField("Model") != null) {
                        // 获取型号字段值
                        String modelValue = $standardProduct.getStringField("Model");
                        // 检查是否匹配无效值模式
                        if (pattern.matcher(modelValue).matches()) {
                            // 如果匹配，设置为默认的无效型号值
                            $standardProduct.setField("Model", "Unknown Model");
                           RuleLogUtil.info("Set model to 'Unknown Model' due to invalid pattern: " + patternStr);
                        }
                    }

                    // 检查标题是否为无效值
                    if ($standardProduct.hasField("Title") && $standardProduct.getStringField("Title") != null) {
                        // 获取标题字段值
                        String titleValue = $standardProduct.getStringField("Title");
                        // 检查是否匹配无效值模式
                        if (pattern.matcher(titleValue).matches()) {
                            // 如果匹配，设置为默认的无效标题值
                            $standardProduct.setField("Title", "Unknown Title");
                           RuleLogUtil.info("Set title to 'Unknown Title' due to invalid pattern: " + patternStr);
                        }
                    }

                    // 检查品类是否为无效值
                    if ($standardProduct.hasField("Category") && $standardProduct.getStringField("Category") != null) {
                        // 获取品类字段值
                        String categoryValue = $standardProduct.getStringField("Category");
                        // 检查是否匹配无效值模式
                        if (pattern.matcher(categoryValue).matches()) {
                            // 如果匹配，设置为默认的无效品类值
                            $standardProduct.setField("Category", "Unknown Category");
                           RuleLogUtil.info("Set category to 'Unknown Category' due to invalid pattern: " + patternStr);
                        }
                    }
                } catch (Exception e) {
                    // 捕获并打印正则表达式编译中的异常
                    System.err.println("Invalid regex pattern: " + patternStr + ", error: " + e.getMessage());
                }
            }
        }
end

// 处理空值和特殊值
// 该规则用于处理空值和常见的无效值（如null、n/a等）
rule "Handle Null And Special Values"
    // no-loop true 表示该规则执行后不会重新激活自己，防止无限循环
    no-loop true
    // when部分定义规则的条件（LHS - Left Hand Side）
    when
        // 匹配ProductDataDTO对象（源数据对象）
        $productData: ProductDataDTO()
        // 匹配DynamicStandardProduct对象（标准化产品对象）
        $standardProduct: DynamicStandardProduct()
    // then部分定义满足条件时要执行的动作（RHS - Right Hand Side）
    then
        // 定义处理空值和特殊值的默认列表
        String[] invalidValues = {"", "null", "n/a", "na", "none", "unknown", "does not apply", "not applicable"};

        // 处理品牌字段
        if (!$standardProduct.hasField("Brand") || $standardProduct.getStringField("Brand") == null) {
            // 如果品牌字段不存在或为空，设置为默认的无效品牌值
            $standardProduct.setField("Brand", "Unknown Brand");
           RuleLogUtil.info("Set brand to 'Unknown Brand' due to null value");
        } else {
            // 获取品牌字段值并转换为小写以便匹配
            String brandValue = $standardProduct.getStringField("Brand").trim().toLowerCase();
            // 检查是否为无效值
            boolean isInvalid = false;
            for (String invalidValue : invalidValues) {
                if (brandValue.equals(invalidValue)) {
                    isInvalid = true;
                    break;
                }
            }
            // 如果是无效值，设置为默认的无效品牌值
            if (isInvalid) {
                $standardProduct.setField("Brand", "Unknown Brand");
               RuleLogUtil.info("Set brand to 'Unknown Brand' due to invalid value: " + brandValue);
            }
        }

        // 处理颜色字段
        if (!$standardProduct.hasField("Color") || $standardProduct.getStringField("Color") == null) {
            // 如果颜色字段不存在或为空，设置为默认的无效颜色值
            $standardProduct.setField("Color", "Unknown Color");
           RuleLogUtil.info("Set color to 'Unknown Color' due to null value");
        } else {
            // 获取颜色字段值并转换为小写以便匹配
            String colorValue = $standardProduct.getStringField("Color").trim().toLowerCase();
            // 检查是否为无效值
            boolean isInvalid = false;
            for (String invalidValue : invalidValues) {
                if (colorValue.equals(invalidValue)) {
                    isInvalid = true;
                    break;
                }
            }
            // 如果是无效值，设置为默认的无效颜色值
            if (isInvalid) {
                $standardProduct.setField("Color", "Unknown Color");
               RuleLogUtil.info("Set color to 'Unknown Color' due to invalid value: " + colorValue);
            }
        }

        // 处理存储字段
        if (!$standardProduct.hasField("Storage") || $standardProduct.getStringField("Storage") == null) {
            // 如果存储字段不存在或为空，设置为默认的无效存储值
            $standardProduct.setField("Storage", "Unknown Storage");
           RuleLogUtil.info("Set storage to 'Unknown Storage' due to null value");
        } else {
            // 获取存储字段值并转换为小写以便匹配
            String storageValue = $standardProduct.getStringField("Storage").trim().toLowerCase();
            // 检查是否为无效值
            boolean isInvalid = false;
            for (String invalidValue : invalidValues) {
                if (storageValue.equals(invalidValue)) {
                    isInvalid = true;
                    break;
                }
            }
            // 如果是无效值，设置为默认的无效存储值
            if (isInvalid) {
                $standardProduct.setField("Storage", "Unknown Storage");
               RuleLogUtil.info("Set storage to 'Unknown Storage' due to invalid value: " + storageValue);
            }
        }

        // 处理型号字段
        if (!$standardProduct.hasField("Model") || $standardProduct.getStringField("Model") == null) {
            // 如果型号字段不存在或为空，设置为默认的无效型号值
            $standardProduct.setField("Model", "Unknown Model");
           RuleLogUtil.info("Set model to 'Unknown Model' due to null value");
        } else {
            // 获取型号字段值并转换为小写以便匹配
            String modelValue = $standardProduct.getStringField("Model").trim().toLowerCase();
            // 检查是否为无效值
            boolean isInvalid = false;
            for (String invalidValue : invalidValues) {
                if (modelValue.equals(invalidValue)) {
                    isInvalid = true;
                    break;
                }
            }
            // 如果是无效值，设置为默认的无效型号值
            if (isInvalid) {
                $standardProduct.setField("Model", "Unknown Model");
               RuleLogUtil.info("Set model to 'Unknown Model' due to invalid value: " + modelValue);
            }
        }
end
