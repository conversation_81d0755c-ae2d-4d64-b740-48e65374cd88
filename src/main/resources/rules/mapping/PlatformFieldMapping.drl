// 文件: src/main/resources/rules/mapping/PlatformFieldMapping.drl
// 功能: 平台字段映射规则
// 作者: 数据处理组
// 创建时间: 2025-07-29

package rules.mapping;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.PlatformFieldMapping;
import ai.pricefox.mallfox.domain.standard.StandardField;
import java.util.Map;

/**
 * 规则名称：平台字段映射规则
 * 功能描述：将平台特定字段映射到标准字段
 * 触发条件：
 *   1. 存在Map类型的原始数据
 *   2. 存在DynamicStandardProduct对象
 *   3. 存在匹配的PlatformFieldMapping和StandardField
 * 处理逻辑：
 *   1. 获取源字段值
 *   2. 将源字段值映射到标准字段
 *   3. 记录映射日志
 * 优化说明：强化异常处理，优化日志记录
 */
rule "Platform Field Mapping"
    no-loop true
    when
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        $platformFieldMapping: PlatformFieldMapping(platformCode == $originalData.get("platformCode"))
        $standardField: StandardField(fieldCode == $platformFieldMapping.getStandardFieldCode())
    then {
        String sourceFieldName = $platformFieldMapping.getSourceFieldName();
        String standardFieldName = $standardField.getFieldNameEn();

        try {
            // 从原始数据Map中获取值
            Object value = $originalData.get(sourceFieldName);
            
            if (value != null && !value.toString().trim().isEmpty()) {
                $standardProduct.setField(standardFieldName, value.toString());
                RuleLogUtil.info("[平台字段映射规则] 字段映射完成: '" + sourceFieldName + "' -> '" + standardFieldName + "' = '" + value + "'");
            }
        } catch (Exception e) {
            RuleLogUtil.error("[平台字段映射规则] 字段映射异常: 源字段'" + sourceFieldName + "' 处理错误: " + e.getMessage());
        }
    }
end
