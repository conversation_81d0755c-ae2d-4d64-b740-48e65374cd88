// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.ProductDataDTO;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.NormalizationLibrary;
import ai.pricefox.mallfox.enums.ValueTypeEnum;
import java.util.regex.Pattern;

// 基于归一化库的动态归一化规则
// 该规则用于将字段值从原始值转换为标准化值
rule "Dynamic Normalization Using Library"
    // no-loop true 表示该规则执行后不会重新激活自己，防止无限循环
    no-loop true
    // when部分定义规则的条件（LHS - Left Hand Side）
    when
        // 匹配ProductDataDTO对象（源数据对象）
        $productData: ProductDataDTO()
        // 匹配DynamicStandardProduct对象（标准化产品对象）
        $standardProduct: DynamicStandardProduct()
        // 匹配NormalizationLibrary对象，要求originalValue和normalizedValue都不为空
        $normLib: NormalizationLibrary(originalValue != null, normalizedValue != null)
    // then部分定义满足条件时要执行的动作（RHS - Right Hand Side）
    then
        // 从归一化库中获取原始值和标准化值
        String originalValue = $normLib.getOriginalValue();
        String normalizedValue = $normLib.getNormalizedValue();
        // 获取值类型（品牌、颜色、存储、型号等）
        ValueTypeEnum valueType = $normLib.getValueType();

        // 对品牌进行归一化处理
        if (ValueTypeEnum.BRAND == valueType &&
            $standardProduct.hasField("Brand") &&
            $standardProduct.getStringField("Brand") != null) {
            
            String brandValue = $standardProduct.getStringField("Brand").trim();
            String normBrand = originalValue.trim();
            
            // 记录匹配尝试
            RuleLogUtil.info("尝试匹配品牌: '" + brandValue + "' 与规则: '" + normBrand + "'");
            
            // 严格匹配：完全相等（忽略大小写）
            if (brandValue.equalsIgnoreCase(normBrand)) {
                $standardProduct.setField("Brand", normalizedValue);
                RuleLogUtil.info("Normalized brand: " + originalValue + " -> " + normalizedValue);
            } else if (Pattern.compile("[\\*\\+\\?\\{\\}\\[\\]\\(\\)\\|\\\\]").matcher(normBrand).find()) {
                // 如果原始值包含正则表达式特殊字符，则使用正则匹配
                try {
                    if (Pattern.matches(normBrand, brandValue)) {
                        $standardProduct.setField("Brand", normalizedValue);
                        RuleLogUtil.info("Normalized brand: " + originalValue + " -> " + normalizedValue);
                    }
                } catch (Exception e) {
                    RuleLogUtil.warn("Invalid regex pattern for brand normalization: " + normBrand + ", error: " + e.getMessage());
                }
            }
        }
        // 对颜色进行归一化处理
        else if (ValueTypeEnum.COLOR == valueType &&
                 $standardProduct.hasField("Color") &&
                 $standardProduct.getStringField("Color") != null &&
                 $standardProduct.getStringField("Color").equalsIgnoreCase(originalValue)) {
            // 如果字段值与原始值匹配（忽略大小写），则设置为标准化值
            $standardProduct.setField("Color", normalizedValue);
           RuleLogUtil.info("Normalized color: " + originalValue + " -> " + normalizedValue);
        }
        // 对存储进行归一化处理
        else if (ValueTypeEnum.STORAGE == valueType &&
                 $standardProduct.hasField("Storage") &&
                 $standardProduct.getStringField("Storage") != null &&
                 $standardProduct.getStringField("Storage").equalsIgnoreCase(originalValue)) {
            // 如果字段值与原始值匹配（忽略大小写），则设置为标准化值
            $standardProduct.setField("Storage", normalizedValue);
           RuleLogUtil.info("Normalized storage: " + originalValue + " -> " + normalizedValue);
        }
        // 对型号进行归一化处理
        else if (ValueTypeEnum.MODEL == valueType &&
                 $standardProduct.hasField("Model") &&
                 $standardProduct.getStringField("Model") != null &&
                 $standardProduct.getStringField("Model").equalsIgnoreCase(originalValue)) {
            // 如果字段值与原始值匹配（忽略大小写），则设置为标准化值
            $standardProduct.setField("Model", normalizedValue);
           RuleLogUtil.info("Normalized model: " + originalValue + " -> " + normalizedValue);
        }
end

// 使用正则表达式进行归一化
// 该规则用于处理需要通过正则表达式进行模式匹配和替换的归一化场景
rule "Regex Based Normalization"
    // no-loop true 表示该规则执行后不会重新激活自己，防止无限循环
    no-loop true
    // when部分定义规则的条件（LHS - Left Hand Side）
    when
        // 匹配ProductDataDTO对象（源数据对象）
        $productData: ProductDataDTO()
        // 匹配DynamicStandardProduct对象（标准化产品对象）
        $standardProduct: DynamicStandardProduct()
        // 匹配NormalizationLibrary对象，要求originalValue不为空且包含正则表达式特殊字符
        $normLib: NormalizationLibrary(originalValue != null, originalValue matches ".*[\\*\\+\\?\\{\\}\\[\\]\\(\\)\\|\\\\].*")
    // then部分定义满足条件时要执行的动作（RHS - Right Hand Side）
    then
        // 使用try-catch块处理可能的异常
        try {
            // 获取正则表达式模式和替换字符串
            String pattern = $normLib.getOriginalValue();
            String replacement = $normLib.getNormalizedValue();
            // 获取值类型
            ValueTypeEnum valueTypeRegex = $normLib.getValueType();

            // 对颜色字段应用正则归一化
            if (ValueTypeEnum.COLOR == valueTypeRegex && $standardProduct.hasField("Color") && $standardProduct.getStringField("Color") != null) {
                // 使用正则表达式替换颜色字段中的匹配内容
                String normalizedColor = $standardProduct.getStringField("Color").replaceAll(pattern, replacement);
                // 如果替换后的内容与原内容不同，则更新字段值
                if (!normalizedColor.equals($standardProduct.getStringField("Color"))) {
                    $standardProduct.setField("Color", normalizedColor);
                   RuleLogUtil.info("Regex normalized color: " + $standardProduct.getStringField("Color") + " -> " + normalizedColor);
                }
            }
            // 对存储字段应用正则归一化
            else if (ValueTypeEnum.STORAGE == valueTypeRegex && $standardProduct.hasField("Storage") && $standardProduct.getStringField("Storage") != null) {
                // 使用正则表达式替换存储字段中的匹配内容
                String normalizedStorage = $standardProduct.getStringField("Storage").replaceAll(pattern, replacement);
                // 如果替换后的内容与原内容不同，则更新字段值
                if (!normalizedStorage.equals($standardProduct.getStringField("Storage"))) {
                    $standardProduct.setField("Storage", normalizedStorage);
                   RuleLogUtil.info("Regex normalized storage: " + $standardProduct.getStringField("Storage") + " -> " + normalizedStorage);
                }
            }
        } catch (Exception e) {
            // 捕获并打印正则表达式处理中的异常
            System.err.println("Error in regex normalization: " + e.getMessage());
        }
end