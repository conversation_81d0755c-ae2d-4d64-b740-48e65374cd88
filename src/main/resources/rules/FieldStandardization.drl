// 声明规则包名，用于组织和管理规则
package rules;

// 导入需要用到的Java类
import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import java.util.Map;
import java.util.Set;

// 字段标准化规则
// 该规则用于标准化字段名称，解决字段名大小写不一致和重复问题
rule "Standardize Field Names"
    no-loop true
    salience 100 // 提高优先级，确保最先执行
    when
        $standardProduct: DynamicStandardProduct()
    then
        Map<String, Object> fields = $standardProduct.getFields();
        Map<String, Object> normalizedFields = new java.util.HashMap<>();

        // 优先保留首字母大写形式的字段（标准字段名格式）
        for (Map.Entry<String, Object> entry : fields.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();

            // 转换全大写的字段名为首字母大写形式
            String normalizedFieldName = normalizeFieldName(fieldName);

            // 如果标准化后的字段名已存在且当前字段是全大写形式，则跳过
            // 否则添加或替换字段
            if (!normalizedFields.containsKey(normalizedFieldName) ||
                !isAllUppercase(fieldName)) {
                normalizedFields.put(normalizedFieldName, fieldValue);
            }
        }

        // 清空原字段并添加标准化后的字段
        fields.clear();
        fields.putAll(normalizedFields);

        RuleLogUtil.info("字段标准化完成，处理了 " + normalizedFields.size() + " 个字段");
end

// 检查字符串是否全为大写
function boolean isAllUppercase(String str) {
    if (str == null || str.isEmpty()) {
        return false;
    }
    return str.equals(str.toUpperCase()) && !str.equals(str.toLowerCase());
}

// 将字符串首字母大写
function String capitalize(String str) {
    if (str == null || str.isEmpty()) {
        return str;
    }
    return Character.toUpperCase(str.charAt(0)) + str.substring(1);
}

// 标准化单个字段名的函数
function String normalizeFieldName(String fieldName) {
    if (fieldName == null || fieldName.isEmpty()) {
        return fieldName;
    }

    // 如果是下划线分隔的大写形式，转换为首字母大写驼峰形式
    if (fieldName.contains("_") && fieldName.equals(fieldName.toUpperCase())) {
        StringBuilder sb = new StringBuilder();
        String[] parts = fieldName.toLowerCase().split("_");
        for (String part : parts) {
            if (!part.isEmpty()) {
                sb.append(Character.toUpperCase(part.charAt(0)))
                  .append(part.substring(1));
            }
        }
        return sb.toString();
    }

    // 如果是全大写且不含下划线，转换为首字母大写
    if (isAllUppercase(fieldName) && !fieldName.contains("_")) {
        return capitalize(fieldName.toLowerCase());
    }

    // 如果是驼峰命名且首字母小写，转换为首字母大写
    if (Character.isLowerCase(fieldName.charAt(0))) {
        return capitalize(fieldName);
    }

    return fieldName;
}


