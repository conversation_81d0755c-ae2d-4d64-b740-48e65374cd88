// 文件: src/main/resources/rules/standardization/ColorStandardization.drl
// 功能: 颜色标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardColor;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：Process Color Standardization
 * 功能描述：实现颜色字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardColor标准颜色库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取颜色信息
 *   3. 最后使用原始颜色数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Color Standardization"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        // 获取所有标准颜色库
        $colors: List() from collect(StandardColor())
    then {
        boolean processed = false;
        String color = $standardProduct.getStringField("Color");

        // 第一阶段：标准库匹配
        if (color != null && !color.trim().isEmpty() && !processed) {
            String originalColor = color.trim();
            // 使用contains代替直接匹配
            for (Object colorObj : $colors) {
                StandardColor standardColor = (StandardColor) colorObj;
                if (originalColor.equalsIgnoreCase(standardColor.getCodeNameLevel1()) ||
                    originalColor.equalsIgnoreCase(standardColor.getCodeNameLevel2())) {
                    $standardProduct.setField("Color", standardColor.getCodeNameLevel2());
                    RuleLogUtil.info("[颜色标准化规则] 颜色标准库匹配成功: 原始颜色='{}' -> 标准颜色='{}'", originalColor, standardColor.getColorNameEn());
                    processed = true;
                    break;
                }
            }
        }

        // 第二阶段：标题提取
        String title = (String)$originalData.get("title");
        if (title != null && !processed) {
            title = title.toLowerCase();

            for (Object colorObj : $colors) {
                StandardColor standardColor = (StandardColor) colorObj;
                String codeNameLevel1 = standardColor.getCodeNameLevel1();
                String codeNameLevel2 = standardColor.getCodeNameLevel2();

                if (codeNameLevel2 != null) {
                    String normalizedCodeNameLevel2 = codeNameLevel2.replaceAll("\\s+", "").toLowerCase();
                    String normalizedTitle = title.replaceAll("\\s+", "");
                    if (normalizedTitle.contains(normalizedCodeNameLevel2)) {
                        $standardProduct.setField("Color", codeNameLevel1);
                        RuleLogUtil.info("[颜色标准化规则] 从商品标题中提取到颜色: 标题='"+title+"' -> 匹配颜色='"+codeNameLevel1+"'");
                        processed = true;
                        break;
                    }
                }
            }
        }

        // 第三阶段：原始数据兜底
        if (!processed && ($standardProduct.getStringField("Color") == null || $standardProduct.getStringField("Color").trim().isEmpty())) {
            String rawColor = (String)$originalData.get("color");
            if (rawColor != null && !rawColor.trim().isEmpty()) {
                $standardProduct.setField("Color", rawColor);
                RuleLogUtil.warn("[颜色标准化规则] 使用原始颜色数据作为兜底方案: 颜色='"+rawColor+"'");
            }
        }
    }
end