// 文件: src/main/resources/rules/standardization/ModelStandardization.drl
// 功能: 型号标准化处理规则
// 作者: 商品数据组
// 创建时间: 2025-07-29
// 修改时间: 2025-07-29（优化注释和日志）

package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardModel;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：Process Model Standardization
 * 功能描述：实现型号字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardModel标准型号库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取型号信息
 *   3. 最后使用原始型号数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Model Standardization"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        // 获取所有标准型号库
        $models: List() from collect(StandardModel())
    then {
        boolean processed = false;
        String model = $standardProduct.getFields().get("Raw0000006").toString();

        // 第一阶段：标准库匹配
        if (model != null && !model.trim().isEmpty() && !processed) {
            String originalModel = model.trim();
            // 使用contains代替直接匹配
            for (Object modelObj : $models) {
                StandardModel standardModel = (StandardModel) modelObj;
                if (originalModel.equalsIgnoreCase(standardModel.getModelName())) {
                    $standardProduct.setField("Raw0000006", standardModel.getModelName());
                    RuleLogUtil.info("[型号标准化规则] 型号标准库匹配成功: 原始型号='"+originalModel+"' -> 标准型号='"+standardModel.getModelName()+"'");
                    processed = true;
                    break;
                }
            }
        }

        // 第二阶段：标题提取
        String title = (String)$originalData.get("title");
        if (title != null && !processed) {
            title = title.toLowerCase();

            for (Object modelObj : $models) {
                StandardModel standardModel = (StandardModel) modelObj;
                String modelName = standardModel.getModelName();

                if (modelName != null && title.contains(modelName.toLowerCase())) {
                    $standardProduct.setField("Raw0000006", modelName);
                    RuleLogUtil.info("[型号标准化规则] 从商品标题中提取到型号: 标题='"+title+"' -> 匹配型号='"+modelName+"'");
                    processed = true;
                    break;
                }
            }

            // 使用正则表达式提取潜在型号
            if (!processed) {
                java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("([A-Za-z]+(?:\\s+[A-Za-z0-9]+)*)");
                java.util.regex.Matcher matcher = pattern.matcher(title);
                String resultModel = null;
                while (matcher.find() && resultModel == null) {
                    String candidate = matcher.group(1);
                    if (candidate.matches(".*\\d+.*")) {
                        resultModel = candidate;
                    }
                }
                if (resultModel == null) {
                    matcher.reset();
                    if (matcher.find()) {
                        resultModel = matcher.group(1);
                    }
                }
                if (resultModel != null) {
                    $standardProduct.setField("Raw0000006", resultModel);
                    RuleLogUtil.info("[型号标准化规则] 通过正则表达式从标题中提取到型号: 型号='"+resultModel+"'");
                    processed = true;
                }
            }
        }

        // 第三阶段：原始数据兜底
        if (!processed && ($standardProduct.getStringField("Model") == null || $standardProduct.getStringField("Model").trim().isEmpty())) {
            String rawModel = (String)$originalData.get("model");
            if (rawModel != null && !rawModel.trim().isEmpty()) {
                $standardProduct.setField("Raw0000006", rawModel);
                RuleLogUtil.warn("[型号标准化规则] 使用原始型号数据作为兜底方案: 型号='"+rawModel+"'");
            }
        }
    }
end