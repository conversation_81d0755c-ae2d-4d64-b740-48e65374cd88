// 文件: src/main/resources/rules/standardization/BrandStandardization.drl
// 功能: 品牌标准化处理规则
// 作者: 商品数据组
// 创建时间: 2025-07-29
// 修改时间: 2025-07-29（优化注释和日志）

package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardBrand;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 规则名称：Process Brand Standardization
 * 功能描述：实现品牌字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardBrand标准品牌库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取品牌信息
 *   3. 最后使用原始品牌数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Brand Standardization"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        // 获取所有标准品牌库
        $brands: List() from collect(StandardBrand())
    then {
        boolean processed = false;
        String brand = $standardProduct.getFields().get("Raw0000004").toString();

        // 第一阶段：标准库匹配
        if (brand != null && !brand.trim().isEmpty() && !processed) {
            String originalBrand = brand.trim();
            String standardizedBrand = matchBrandWithStandardLibrary(originalBrand, $brands);

            if (standardizedBrand != null) {
                $standardProduct.setField("Brand", standardizedBrand);
                RuleLogUtil.info("[品牌标准化规则] 品牌标准库匹配成功: 原始品牌='"+originalBrand+"' -> 标准品牌='"+standardizedBrand+"'");
                processed = true;
            }
        }

        // 第二阶段：标题提取
        String title = (String) $standardProduct.getFields().get("Raw0000005");
        if (title != null && !processed) {
            title = title.toLowerCase();

            for (Object brandObj : $brands) {
                StandardBrand standardBrand = (StandardBrand) brandObj;
                String brandNameEn = standardBrand.getBrandNameEn();

                if (brandNameEn != null && Pattern.compile(Pattern.quote(brandNameEn), Pattern.CASE_INSENSITIVE).matcher(title).find()) {
                    $standardProduct.setField("Brand", brandNameEn);
                    RuleLogUtil.info("[品牌标准化规则] 从商品标题中提取到品牌: 标题='"+title+"' -> 匹配品牌='"+brandNameEn+"'");
                    processed = true;
                    break;
                }
            }
        }

        // 第三阶段：原始数据兜底
        if (!processed && ($standardProduct.getFields().get("Raw0000004") == null)) {
            String rawBrand = (String)$originalData.get("brand");
            if (rawBrand != null && !rawBrand.trim().isEmpty()) {
                $standardProduct.setField("Brand", rawBrand);
                RuleLogUtil.warn("[品牌标准化规则] 使用原始品牌数据作为兜底方案: 品牌='"+rawBrand+"'");
            }
        }
    }
end

/**
 * 功能描述：品牌标准库匹配函数
 * 输入参数：原始品牌值和标准品牌库列表
 * 返回值：匹配到的标准品牌名称
 * 处理逻辑：遍历标准品牌库进行精确匹配
 */
function String matchBrandWithStandardLibrary(String originalValue, List brands) {
    for (Object brandObj : brands) {
        StandardBrand standardBrand = (StandardBrand) brandObj;
        String brandNameEn = standardBrand.getBrandNameEn();
        String brandNameCn = standardBrand.getBrandNameCn();

        if (originalValue.equalsIgnoreCase(brandNameEn) || originalValue.equalsIgnoreCase(brandNameCn)) {
            RuleLogUtil.debug("[品牌标准化规则] 标准库匹配函数找到匹配项: 原始值='"+originalValue+"', 标准品牌='"+brandNameEn+"'");
            return standardBrand.getBrandNameEn();
        }
    }
    return null;
}