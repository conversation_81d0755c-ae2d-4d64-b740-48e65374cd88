// 文件: src/main/resources/rules/standardization/CategoryStandardization.drl
// 功能: 品类标准化处理规则
package rules.standardization;

import ai.pricefox.mallfox.utils.RuleLogUtil;
import ai.pricefox.mallfox.model.dto.DynamicStandardProduct;
import ai.pricefox.mallfox.domain.standard.StandardCategory;
import java.util.List;
import java.util.Map;

/**
 * 规则名称：Process Category Standardization
 * 功能描述：实现品类字段的多级标准化处理
 * 触发条件：
 *   1. 存在原始数据Map对象
 *   2. 存在DynamicStandardProduct标准产品对象
 *   3. 存在StandardCategory标准品类库列表
 * 处理流程：
 *   1. 优先使用标准库精确匹配
 *   2. 其次从标题中提取品类信息
 *   3. 最后使用原始品类数据
 * 处理优先级：salience 100（最高优先级）
 */
rule "Process Category Standardization"
    no-loop true
    when
        // 匹配原始数据Map和标准产品对象
        $originalData: Map()
        $standardProduct: DynamicStandardProduct()
        // 获取所有标准品类库
        $categories: List() from collect(StandardCategory())
    then {
        boolean processed = false;
        String category = $standardProduct.getStringField("Category");

        // 第一阶段：标准库匹配
        if (category != null && !category.trim().isEmpty() && !processed) {
            String originalCategory = category.trim();
            // 使用contains代替直接匹配
            for (Object categoryObj : $categories) {
                StandardCategory standardCategory = (StandardCategory) categoryObj;
                if (originalCategory.equalsIgnoreCase(standardCategory.getCategoryNameCn()) ||
                    originalCategory.equalsIgnoreCase(standardCategory.getCategoryNameEn())) {
                    $standardProduct.setField("Category", standardCategory.getCategoryNameEn());
                    RuleLogUtil.info("[品类标准化规则] 品类标准库匹配成功: 原始品类='"+originalCategory+"' -> 标准品类='"+standardCategory.getCategoryNameEn()+"'");
                    processed = true;
                    break;
                }
            }
        }

        // 第二阶段：标题提取
        String title = (String)$originalData.get("title");
        if (title != null && !processed) {
            title = title.toLowerCase();

            for (Object categoryObj : $categories) {
                StandardCategory standardCategory = (StandardCategory) categoryObj;
                String categoryNameCn = standardCategory.getCategoryNameCn();
                String categoryNameEn = standardCategory.getCategoryNameEn();

                if ((categoryNameCn != null && title.contains(categoryNameCn.toLowerCase())) ||
                    (categoryNameEn != null && title.contains(categoryNameEn.toLowerCase()))) {
                    $standardProduct.setField("Category", categoryNameEn != null ? categoryNameEn : categoryNameCn);
                    RuleLogUtil.info("[品类标准化规则] 从商品标题中提取到品类: 标题='"+title+"' -> 匹配品类='"+ categoryNameEn != null ? categoryNameEn : categoryNameCn+"'");
                    processed = true;
                    break;
                }
            }
        }

        // 第三阶段：原始数据兜底
        if (!processed && ($standardProduct.getStringField("Category") == null || $standardProduct.getStringField("Category").trim().isEmpty())) {
            String rawCategory = (String)$originalData.get("categoryLevel1");
            if (rawCategory != null && !rawCategory.trim().isEmpty()) {
                $standardProduct.setField("Category", rawCategory);
                RuleLogUtil.warn("[品类标准化规则] 使用原始品类数据作为兜底方案: 品类='"+rawCategory+"'");
            }
        }
    }
end