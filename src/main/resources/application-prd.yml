# ===========================================
# 生产环境配置
# ===========================================
spring:
  config:
    activate:
      on-profile: prd  # 激活生产环境
  datasource:
    url: *********************************************************************************************************************************** # 生产数据库URL
    username: root  # 生产数据库用户
    password: PriceFox123!@#1  # 生产数据库密码
    driver-class-name: com.mysql.jdbc.Driver  # MySQL驱动
  data:
    redis:
      host: 127.0.0.1  # Redis生产主机
      port: 6379  # Redis端口
      password:   # Redis密码(生产环境应为空)
      database: 1  # Redis库索引
      timeout: 10000  # 连接超时(毫秒)
  mail:
    host: smtp.office365.com  # 邮件服务器
    port: 587  # 邮件端口
    username: <EMAIL>  # 邮件账号
    password: your-password  # 邮件密码
    properties:
      mail:
        smtp:
          ssl:
            enable: true  # 启用SSL

# ===========================================
# 生产环境日志配置
# ===========================================
logging:
  level:
    # SaToken调试 (生产环境保持DEBUG)
    ai.pricefox.mallfox.config.SaTokenConfigure: DEBUG
    cn.dev33.satoken: DEBUG
    
    # MyBatis调试 (生产环境保持DEBUG)
    ai.pricefox.mallfox.mapper: DEBUG
    com.baomidou.mybatisplus: DEBUG
    
    # 请求日志 (生产环境使用INFO级别)
    ai.pricefox.mallfox.config.RequestLoggingInterceptor: INFO
    ai.pricefox.mallfox.config.ApiLoggingAspect: INFO
    
    # SQL日志 (生产环境关闭详细日志)
    org.apache.ibatis: WARN
    java.sql: WARN
    java.sql.Statement: WARN
    java.sql.PreparedStatement: WARN
    java.sql.ResultSet: WARN

# ===========================================
# 生产环境应用日志配置
# ===========================================
app:
  logging:
    enable-request-logging: true  # 启用请求日志
    enable-api-logging: false  # 生产环境关闭API详细日志
    enable-sql-logging: false  # 生产环境关闭SQL日志
    log-request-headers: false  # 不记录请求头(安全)
    log-request-parameters: true  # 记录请求参数
    log-request-body: false  # 不记录请求体(安全)
    log-response-body: false  # 不记录响应体(安全)
    max-request-body-length: 500  # 请求体最大长度限制
    max-response-body-length: 500  # 响应体最大长度限制

# ===========================================
# OAuth配置 (生产环境)
# ===========================================
google:
  client-id: 928277519574-dpb9c1nel2ivd8pl19d90qps55rkdgse.apps.googleusercontent.com  # Google客户端ID
  client-secret: 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  # Google客户端密钥
  redirect-uri: http://47.251.141.145:28888/api/oauth/google/callback  # 生产环境回调地址

# ===========================================
# 其他服务配置 (生产环境)
# ===========================================
amazon:
  api:
    key:  # 生产环境Amazon API Key
    secret:  # 生产环境Amazon API Secret