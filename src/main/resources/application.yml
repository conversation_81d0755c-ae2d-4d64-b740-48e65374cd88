# ===========================================
# 应用基础配置
# ===========================================
spring:
  application:
    name: mallfox  # 应用名称
  profiles:
    active: dev  # 默认激活开发环境
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss  # 日期格式
    time-zone: GMT+8  # 时区配置
    default-property-inclusion: non_null  # 不序列化null值
    serialization:
      FAIL_ON_EMPTY_BEANS: false  # 空对象不报错
      write-dates-as-timestamps: false  # 日期不写为时间戳
    deserialization:
      fail-on-unknown-properties: false  # 忽略未知属性
  main:
    allow-circular-references: true  # 允许循环引用
  servlet:
    multipart:
      enabled: true  # 启用文件上传
      max-file-size: 500MB  # 最大文件大小
      max-request-size: 1000MB  # 最大请求大小
      file-size-threshold: 2KB  # 文件大小阈值
      resolve-lazily: false  # 非延迟解析

  # ===========================================
  # redis 连接池
  # ===========================================
  data:
    redis:
      lettuce:
        pool:
          max-active: 8  # Redis最大活跃连接
          max-wait: -1  # 最大等待时间(无限)
          max-idle: 8  # 最大空闲连接
          min-idle: 0  # 最小空闲连接

  # ===========================================
  # 邮件服务配置
  # ===========================================
  mail:
    properties:
      mail:
        smtp:
          auth: true  # 启用SMTP认证
          starttls:
            enable: true  # 启用STARTTLS
    default-encoding: UTF-8  # 默认编码

  # ===========================================
  # mysql 连接池
  # ===========================================
  datasource:
    druid:
      initial-size: 30  # 初始连接数
      max-active: 1000  # 最大连接数
      min-idle: 30  # 最小空闲连接
      max-wait: 100000  # 获取连接最大等待时间(毫秒)
      use-unfair-lock: true  # 使用非公平锁
      pool-prepared-statements: false  # 不缓存预编译语句
      validation-query: select 1  # 验证查询SQL
      validation-query-timeout: 2  # 验证查询超时(秒)
      keep-alive: true  # 保持连接活跃
      test-on-borrow: false  # 借出时不测试
      test-on-return: false  # 归还时不测试
      test-while-idle: true  # 空闲时测试
      min-evictable-idle-time-millis: 43200000  # 最小空闲时间(毫秒)
      max-evictable-idle-time-millis: 86400000  # 最大空闲时间(毫秒)

# ===========================================
# 服务器配置
# ===========================================
server:
  port: 28888  # 服务端口
  error:
    whitelabel:
      enabled: false  # 禁用默认错误页
  servlet:
    context-path: /api  # API前缀

# ===========================================
# Sa-Token 认证配置
# ===========================================
sa-token:
  token-name: Authorization  # token名称
  timeout: 2592000  # token有效期(秒) 30天
  active-timeout: -1  # token活跃期(秒) 永不冻结
  is-concurrent: true  # 允许同一账号多地登录
  is-share: true  # 多人登录共用同一token
  token-style: uuid  # token风格
  is-log: true  # 输出操作日志

# ===========================================
# 业务模块配置
# ===========================================
# 销量数据补充
sales:
  supplement:
    enabled: true  # 功能开关
    review-to-sales-ratio: 0.03  # 评论转销量比例
    batch-size: 1000  # 批处理大小

# 线程池配置
thread:
  pool:
    core-size: 10  # 核心线程数
    max-size: 20  # 最大线程数
    queue-capacity: 200  # 队列容量
    keep-alive: 60  # 线程空闲时间(秒)
    thread-name-prefix: async-task-  # 线程名前缀

# 字段追踪
field:
  tracking:
    enabled: true  # 功能开关
    async-enabled: true  # 异步处理
    batch-size: 1000  # 批处理大小
    queue-capacity: 10000  # 队列容量
    process-interval: 5000  # 处理间隔(毫秒)

# 价格历史记录
price:
  history:
    enabled: true  # 功能开关
    async-enabled: true  # 异步处理
    batch-size: 500  # 批处理大小
    retention-days: 365  # 数据保留天数
    cleanup-enabled: true  # 启用清理
    cleanup-interval: 86400000  # 清理间隔(毫秒)
    record-create: true  # 记录创建操作
    record-update: true  # 记录更新操作
    record-delete: true  # 记录删除操作
    minimum-change-threshold: 0.01  # 最小变更阈值
    debug-log-enabled: false  # 调试日志开关

# ===========================================
# 第三方服务配置
# ===========================================
# 阿里云OSS存储
aliyun:
  oss:
    endpoint: oss-us-west-1.aliyuncs.com  # OSS端点
    access-key-id: LTAI5t9girPswJTTfbfjcqhH  # 访问ID
    access-key-secret: ******************************  # 访问密钥
    bucket-name: pricefox  # 存储桶名称

# eBay API配置
ebay:
  api:
    client-id: yunfeiwa-i-PRD-f8e9408e1-f02fd8c9  # 生产客户端ID
    client-secret: PRD-8e9408e1e474-194e-4133-9052-4b69  # 生产客户端密钥
    base-url: https://api.ebay.com/buy/browse/v1  # 生产API地址
    sandbox-base-client-id: yunfeiwa-i-PRD-f8e9408e1-f02fd8c9  # 沙盒客户端ID
    sandbox-base-client-secret: PRD-8e9408e1e474-194e-4133-9052-4b69  # 沙盒客户端密钥
    sandbox-base-url: https://api.sandbox.ebay.com/buy/browse/v1  # 沙盒API地址
    scope: https://api.ebay.com/oauth/api_scope  # 授权范围
    sandbox: false  # 是否使用沙盒环境
    mock-mode: false  # 是否使用模拟模式
    timeout: 30000  # 请求超时(毫秒)
    max-retries: 3  # 最大重试次数
    retry-delay: 1000  # 重试延迟(毫秒)

# BestBuy API配置
bestbuy:
  api:
    key: vcuVaClMGT7uYQANxlBoLybw  # API密钥
    base-url: https://api.bestbuy.com/v1  # API地址
    timeout: 30000  # 请求超时(毫秒)
    max-retries: 3  # 最大重试次数
    retry-delay: 1000  # 重试延迟(毫秒)

# Microsoft Graph API
microsoft:
  graph:
    client-id: 565f1679-80ad-4cce-a3d1-4ee74f1a85b2  # 客户端ID
    tenant-id: 93ba8227-f02f-451c-a4d9-331cb03ee414  # 租户ID
    client-secret: ****************************************  # 客户端密钥
    sender-email: <EMAIL>  # 发件邮箱

# ===========================================
# 密码加密配置
# ===========================================
app:
  password:
    enable-encryption: true  # 启用加密
    secret-key: PriceFoxMallKey!  # 加密密钥
    log-encryption: false  # 是否记录加密日志
    min-length: 6  # 最小密码长度
    max-length: 20  # 最大密码长度
  mail:
    user: <EMAIL>  # 发件人邮箱

# ===========================================
# MyBatis-Plus 配置
# ===========================================
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl  # SLF4J日志实现